# Advanced UPID Configuration API Guide

## Overview

The Advanced UPID Configuration API allows administrators to configure Unique Patient ID (UPID) generation using regex patterns and configurable components. This system supports:

- **Regex-based pattern configuration**
- **Configurable components** (Network ID, Facility ID, Sequence Number)
- **Flexible digit lengths** for each component
- **Multiple format options** (Numeric, Alpha, Alphanumeric)
- **Hospital shortform support**
- **Configuration history and audit trail**

## API Endpoints

### Base URL: `/api/admin/upid-config`

### 1. Create UPID Configuration

**POST** `/api/admin/upid-config`

Creates a new UPID configuration with regex patterns and configurable components.

**Request Body:**
```json
{
  "configName": "Custom Hospital Format",
  "patternTemplate": "HOSPITAL-FACILITY-NETWORK-SEQUENCE",
  "description": "Custom format with hospital shortform",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "01"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-",
    "hospitalShortform": {
      "enabled": true,
      "value": "MED",
      "position": "PREFIX"
    }
  },
  "isDefault": false
}
```

**Response:**
```json
{
  "configId": 4,
  "configName": "Custom Hospital Format",
  "patternRegex": "^[A-Z]{3}-\\d{3}-\\d{2}-\\d{4}-\\d{4}$",
  "patternTemplate": "HOSPITAL-FACILITY-NETWORK-SEQUENCE",
  "description": "Custom format with hospital shortform",
  "networkIdDigits": 2,
  "networkIdValue": "01",
  "facilityIdDigits": 3,
  "facilityIdFormat": "NUMERIC",
  "sequenceDigits": 8,
  "sequenceFormat": "SPLIT",
  "separator": "-",
  "hospitalShortform": "MED",
  "useHospitalShortform": true,
  "examplePattern": "MED-001-01-0000-0001",
  "validationRegex": "^[A-Z]{3}-\\d{3}-\\d{2}-\\d{4}-\\d{4}$",
  "isActive": true,
  "isDefault": false,
  "createdAt": "2025-01-01T10:00:00Z",
  "updatedAt": "2025-01-01T10:00:00Z",
  "version": 0
}
```

### 2. Update UPID Configuration

**PUT** `/api/admin/upid-config/{configId}`

Updates an existing UPID configuration.

**Request Body:**
```json
{
  "description": "Updated description",
  "components": {
    "sequence": {
      "digits": 10,
      "format": "CONTINUOUS"
    }
  },
  "changeReason": "Increased sequence length for scalability"
}
```

### 3. Get All Configurations

**GET** `/api/admin/upid-config`

Retrieves all active UPID configurations.

**Response:**
```json
[
  {
    "configId": 1,
    "configName": "Standard UPID Format",
    "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
    "examplePattern": "001-00-0000-0001",
    "isDefault": true,
    "isActive": true
  },
  {
    "configId": 2,
    "configName": "Compact UPID Format",
    "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
    "examplePattern": "00100000001",
    "isDefault": false,
    "isActive": true
  }
]
```

### 4. Get Configuration by ID

**GET** `/api/admin/upid-config/{configId}`

Retrieves a specific UPID configuration by ID.

### 5. Get Default Configuration

**GET** `/api/admin/upid-config/default`

Retrieves the default UPID configuration.

### 6. Delete Configuration

**DELETE** `/api/admin/upid-config/{configId}`

Soft deletes a UPID configuration (cannot delete default configuration).

### 7. Set as Default

**POST** `/api/admin/upid-config/{configId}/set-default`

Sets a configuration as the default for UPID generation.

### 8. Generate UPID

**POST** `/api/admin/upid-config/{configId}/generate/{facilityId}`

Generates a UPID using a specific configuration.

**Response:**
```json
{
  "upid": "MED-001-01-0000-0001",
  "facilityId": "1",
  "configId": 4,
  "timestamp": "2025-01-01T10:00:00Z"
}
```

### 9. Validate Pattern

**POST** `/api/admin/upid-config/validate-pattern`

Validates a UPID pattern and generates sample UPIDs.

**Request Body:**
```json
{
  "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "00"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-"
  },
  "testFacilityId": "1",
  "sampleCount": 3
}
```

**Response:**
```json
{
  "valid": true,
  "errors": [],
  "warnings": [],
  "generatedRegex": "^\\d{3}-\\d{2}-\\d{4}-\\d{4}$",
  "validationRegex": "^\\d{3}-\\d{2}-\\d{4}-\\d{4}$",
  "sampleUpids": [
    "001-00-0000-0001",
    "001-00-0000-0002",
    "001-00-0000-0003"
  ],
  "patternBreakdown": {
    "facility": "Facility ID (3 digits, NUMERIC)",
    "network": "Network ID (2 digits)",
    "sequence": "Sequence (8 digits, split format)"
  },
  "timestamp": "2025-01-01T10:00:00Z"
}
```

### 10. Get Configuration History

**GET** `/api/admin/upid-config/{configId}/history`

Retrieves the change history for a specific configuration.

**Response:**
```json
[
  {
    "historyId": 1,
    "action": "CREATE",
    "changedBy": "ADMIN",
    "changeReason": "Initial configuration created",
    "createdAt": "2025-01-01T10:00:00Z",
    "oldValues": "",
    "newValues": "{\"configName\":\"Custom Format\",...}"
  },
  {
    "historyId": 2,
    "action": "UPDATE",
    "changedBy": "ADMIN",
    "changeReason": "Updated sequence length",
    "createdAt": "2025-01-01T11:00:00Z",
    "oldValues": "{\"sequenceDigits\":8,...}",
    "newValues": "{\"sequenceDigits\":10,...}"
  }
]
```

## Configuration Components

### Network ID
- **Purpose**: Identifies the network or organization
- **Digits**: 1-5 digits
- **Format**: NUMERIC only
- **Example**: "00", "01", "99"

### Facility ID
- **Purpose**: Identifies the specific facility/hospital
- **Digits**: 1-10 digits
- **Format**: NUMERIC, ALPHA, ALPHANUMERIC
- **Examples**: 
  - NUMERIC: "001", "123"
  - ALPHA: "ABC", "XYZ"
  - ALPHANUMERIC: "A01", "X1Y"

### Sequence Number
- **Purpose**: Unique incremental number for each patient
- **Digits**: 4-12 digits
- **Format**: SPLIT (e.g., "0000-0001") or CONTINUOUS (e.g., "00000001")
- **Auto-increment**: Managed automatically per facility

### Hospital Shortform
- **Purpose**: Hospital/organization identifier
- **Length**: 1-20 characters
- **Format**: Uppercase letters
- **Position**: PREFIX, SUFFIX, or MIDDLE
- **Example**: "MED", "HSP", "CARE"

## Pattern Templates

### Supported Placeholders:
- `NETWORK` or `NN`: Network ID component
- `FACILITY` or `FFF` or `XXX`: Facility ID component
- `SEQUENCE` or `NNNN`: Sequence number component
- `HOSPITAL` or `HHH`: Hospital shortform component

### Example Templates:
1. `FACILITY-NETWORK-SEQUENCE` → "001-00-0000-0001"
2. `HOSPITAL-FACILITY-SEQUENCE` → "MED-001-0000-0001"
3. `FACILITY-SEQUENCE` → "001-00000001"
4. `NETWORK-FACILITY-HOSPITAL-SEQUENCE` → "00-001-MED-0001"

## Error Handling

### Common Error Responses:

**400 Bad Request:**
```json
{
  "error": "Configuration name 'Standard Format' already exists"
}
```

**404 Not Found:**
```json
{
  "error": "Configuration not found: 999"
}
```

**409 Conflict:**
```json
{
  "error": "Cannot delete default configuration"
}
```

## Usage Examples

### Example 1: Create a Simple Numeric Format
```bash
curl -X POST http://localhost:8080/api/admin/upid-config \
  -H "Content-Type: application/json" \
  -d '{
    "configName": "Simple Numeric",
    "patternTemplate": "FACILITY-SEQUENCE",
    "description": "Simple facility + sequence format",
    "components": {
      "facilityId": {"digits": 3, "format": "NUMERIC"},
      "sequence": {"digits": 6, "format": "CONTINUOUS"},
      "separator": ""
    }
  }'
```

### Example 2: Create a Hospital-based Format
```bash
curl -X POST http://localhost:8080/api/admin/upid-config \
  -H "Content-Type: application/json" \
  -d '{
    "configName": "Hospital Format",
    "patternTemplate": "HOSPITAL-FACILITY-SEQUENCE",
    "description": "Hospital shortform with facility and sequence",
    "components": {
      "facilityId": {"digits": 2, "format": "NUMERIC"},
      "sequence": {"digits": 8, "format": "SPLIT"},
      "separator": "-",
      "hospitalShortform": {"enabled": true, "value": "CARE"}
    }
  }'
```

### Example 3: Generate UPID
```bash
curl -X POST http://localhost:8080/api/admin/upid-config/1/generate/5
```

This comprehensive API allows administrators to create flexible UPID generation patterns that can adapt to different organizational requirements and standards.
