# ✅ **Correct UPID Configuration JSON Examples**

## 🎯 **Working JSON Examples for LookupValue Table Structure**

### **1. Standard Format (Default)**
```json
{
  "configName": "Standard UPID Format",
  "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
  "description": "Standard UPID format with facility prefix, network ID, and split sequence number",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "00"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-"
  },
  "isDefault": true
}
```
**Generated UPID**: `001-00-0000-0001`
**Stored in LookupValue as**:
- `UPID_CONFIG` → `STANDARD_UPID_FORMAT`
- `UPID_CONFIG_PATTERN` → `FACILITY-NETWORK-SEQUENCE`
- `UPID_CONFIG_NETWORK` → `2|NUMERIC|00`
- `UPID_CONFIG_FACILITY` → `3|NUMERIC`
- `UPID_CONFIG_SEQUENCE` → `8|SPLIT`
- `UPID_CONFIG_SEPARATOR` → `-`

### **2. Hospital Shortform Format**
```json
{
  "configName": "Hospital Shortform Format",
  "patternTemplate": "HOSPITAL-FACILITY-NETWORK-SEQUENCE",
  "description": "UPID format with hospital shortform prefix",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "01"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-",
    "hospitalShortform": {
      "enabled": true,
      "value": "MED"
    }
  },
  "isDefault": false
}
```
**Generated UPID**: `MED-001-01-0000-0001`
**Stored in LookupValue as**:
- `UPID_CONFIG` → `HOSPITAL_SHORTFORM_FORMAT`
- `UPID_CONFIG_PATTERN` → `HOSPITAL-FACILITY-NETWORK-SEQUENCE`
- `UPID_CONFIG_NETWORK` → `2|NUMERIC|01`
- `UPID_CONFIG_FACILITY` → `3|NUMERIC`
- `UPID_CONFIG_SEQUENCE` → `8|SPLIT`
- `UPID_CONFIG_SEPARATOR` → `-`
- `UPID_CONFIG_HOSPITAL` → `true|MED`

### **3. Compact Format (No Separators)**
```json
{
  "configName": "Compact UPID Format",
  "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
  "description": "Compact UPID format without separators",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "00"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "CONTINUOUS"
    },
    "separator": ""
  },
  "isDefault": false
}
```
**Generated UPID**: `00100000001`
**Stored in LookupValue as**:
- `UPID_CONFIG` → `COMPACT_UPID_FORMAT`
- `UPID_CONFIG_PATTERN` → `FACILITY-NETWORK-SEQUENCE`
- `UPID_CONFIG_NETWORK` → `2|NUMERIC|00`
- `UPID_CONFIG_FACILITY` → `3|NUMERIC`
- `UPID_CONFIG_SEQUENCE` → `8|CONTINUOUS`
- `UPID_CONFIG_SEPARATOR` → `` (empty)

### **4. Alpha Facility Format**
```json
{
  "configName": "Alpha Facility Format",
  "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
  "description": "UPID format with alphabetic facility codes",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "02"
    },
    "facilityId": {
      "digits": 3,
      "format": "ALPHA"
    },
    "sequence": {
      "digits": 6,
      "format": "CONTINUOUS"
    },
    "separator": "-"
  },
  "isDefault": false
}
```
**Generated UPID**: `ABC-02-000001`

### **5. Medical Center Format**
```json
{
  "configName": "Medical Center Format",
  "patternTemplate": "HOSPITAL-FACILITY-SEQUENCE",
  "description": "Medical center specific format",
  "components": {
    "facilityId": {
      "digits": 4,
      "format": "ALPHA"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "_",
    "hospitalShortform": {
      "enabled": true,
      "value": "MC"
    }
  },
  "isDefault": false
}
```
**Generated UPID**: `MC_ABCD_0000_0001`

### **6. Network First Format**
```json
{
  "configName": "Network First Format",
  "patternTemplate": "NETWORK-FACILITY-SEQUENCE",
  "description": "Network ID first, then facility and sequence",
  "components": {
    "networkId": {
      "digits": 3,
      "format": "NUMERIC",
      "value": "100"
    },
    "facilityId": {
      "digits": 2,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-"
  },
  "isDefault": false
}
```
**Generated UPID**: `100-01-0000-0001`

### **7. Minimal Format**
```json
{
  "configName": "Minimal Format",
  "patternTemplate": "FACILITY-SEQUENCE",
  "description": "Minimal UPID with just facility and sequence",
  "components": {
    "facilityId": {
      "digits": 2,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 6,
      "format": "CONTINUOUS"
    },
    "separator": ""
  },
  "isDefault": false
}
```
**Generated UPID**: `01000001`

## 🔧 **How Data is Stored in LookupValue Table**

### **Table Structure:**
```sql
CREATE TABLE lookup_values (
    id UUID PRIMARY KEY,
    category VARCHAR(255),
    code VARCHAR(255),
    display_name VARCHAR(255),
    sort_order INT,
    active BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### **Storage Pattern:**
1. **Main Config**: `category = 'UPID_CONFIG'`, `code = 'CONFIG_NAME'`
2. **Pattern**: `category = 'UPID_CONFIG_PATTERN'`, `code = 'CONFIG_NAME'`, `display_name = 'FACILITY-NETWORK-SEQUENCE'`
3. **Network**: `category = 'UPID_CONFIG_NETWORK'`, `code = 'CONFIG_NAME'`, `display_name = 'digits|format|value'`
4. **Facility**: `category = 'UPID_CONFIG_FACILITY'`, `code = 'CONFIG_NAME'`, `display_name = 'digits|format'`
5. **Sequence**: `category = 'UPID_CONFIG_SEQUENCE'`, `code = 'CONFIG_NAME'`, `display_name = 'digits|format'`
6. **Separator**: `category = 'UPID_CONFIG_SEPARATOR'`, `code = 'CONFIG_NAME'`, `display_name = 'separator'`
7. **Hospital**: `category = 'UPID_CONFIG_HOSPITAL'`, `code = 'CONFIG_NAME'`, `display_name = 'enabled|value'`

### **Example Storage for "Standard UPID Format":**
```sql
INSERT INTO lookup_values VALUES
('uuid1', 'UPID_CONFIG', 'STANDARD_UPID_FORMAT', 'Standard UPID format...', 1, true, now(), now()),
('uuid2', 'UPID_CONFIG_PATTERN', 'STANDARD_UPID_FORMAT', 'FACILITY-NETWORK-SEQUENCE', 0, true, now(), now()),
('uuid3', 'UPID_CONFIG_NETWORK', 'STANDARD_UPID_FORMAT', '2|NUMERIC|00', 0, true, now(), now()),
('uuid4', 'UPID_CONFIG_FACILITY', 'STANDARD_UPID_FORMAT', '3|NUMERIC', 0, true, now(), now()),
('uuid5', 'UPID_CONFIG_SEQUENCE', 'STANDARD_UPID_FORMAT', '8|SPLIT', 0, true, now(), now()),
('uuid6', 'UPID_CONFIG_SEPARATOR', 'STANDARD_UPID_FORMAT', '-', 0, true, now(), now()),
('uuid7', 'UPID_CONFIG_HOSPITAL', 'STANDARD_UPID_FORMAT', 'false|', 0, true, now(), now());
```

## ✅ **Benefits of This Approach**

1. **✅ Uses Existing Table**: No new tables needed
2. **✅ Flexible Storage**: Can store any configuration combination
3. **✅ Easy Queries**: Simple SQL queries to retrieve configurations
4. **✅ Scalable**: Can add new configuration types easily
5. **✅ Consistent**: Uses same pattern as other lookup data
6. **✅ Maintainable**: Easy to understand and modify

This flattened structure ensures that all UPID configuration data fits perfectly within the existing LookupValue table constraints while maintaining full functionality!
