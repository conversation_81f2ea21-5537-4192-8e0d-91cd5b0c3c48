# UPID Configuration Using LookupValue Table - Implementation Guide

## Overview

I have successfully updated the UPID configuration system to use the existing `lookup_values` table instead of creating separate configuration tables. This approach provides all the requested functionality while leveraging the existing infrastructure.

## ✅ **Key Features Implemented**

### 1. **Configurable Components Using LookupValue**
- **Network ID**: Stored in `UPID_CONFIG_{UUID}` category with key `NETWORK_ID_DIGITS` and `NETWORK_ID_VALUE`
- **Hospital Shortform**: Stored with keys `HOSPITAL_SHORTFORM` and `USE_HOSPITAL_SHORTFORM`
- **Facility ID**: Configurable digits and format (NUMERIC, ALPHA, ALPHANUMERIC)
- **Sequence Number**: Configurable digits with SPLIT or CONTINUOUS format

### 2. **LookupValue Categories Used**
- `UPID_CONFIG` - Main configuration entries
- `UPID_CONFIG_{UUID}` - Configuration components for each config
- `UPID_NETWORK_ID` - Available network ID options
- `UPID_FACILITY_FORMAT` - Facility ID format options
- `UPID_SEQUENCE_FORMAT` - Sequence format options
- `UPID_HOSPITAL_SHORTFORM` - Hospital shortform options
- `UPID_SEQUENCE_COUNTER` - Sequence counters per facility
- `UPID_CONFIG_HISTORY` - Configuration change history

### 3. **Admin API Endpoints**
All endpoints work with the LookupValue-based storage:

```
POST   /api/admin/upid-config                    - Create configuration
PUT    /api/admin/upid-config/{id}               - Update configuration
GET    /api/admin/upid-config                    - List all configurations
GET    /api/admin/upid-config/{id}               - Get specific configuration
GET    /api/admin/upid-config/default            - Get default configuration
DELETE /api/admin/upid-config/{id}               - Delete configuration
POST   /api/admin/upid-config/{id}/set-default   - Set as default
POST   /api/admin/upid-config/{id}/generate/{facilityId} - Generate UPID
POST   /api/admin/upid-config/validate-pattern   - Validate pattern
GET    /api/admin/upid-config/{id}/history       - Get configuration history
```

## **Database Migration**

The migration `V5__Add_upid_configuration_lookup_values.sql` creates:

1. **Default Configurations**:
   - Standard UPID Format (001-00-0000-0001)
   - Compact UPID Format (00100000001)
   - Hospital Shortform Format (HSP-001-0000-0001)

2. **Predefined Options**:
   - Network ID values (00, 01, 02, 99)
   - Facility formats (NUMERIC, ALPHA, ALPHANUMERIC)
   - Sequence formats (SPLIT, CONTINUOUS)
   - Hospital shortforms (HSP, MED, CARE, CLINIC, INST)

3. **Indexes** for performance optimization

## **Usage Examples**

### Create Custom Configuration
```bash
curl -X POST http://localhost:8080/api/admin/upid-config \
  -H "Content-Type: application/json" \
  -d '{
    "configName": "Custom Medical Center",
    "patternTemplate": "HOSPITAL-FACILITY-NETWORK-SEQUENCE",
    "description": "Medical center format with shortform",
    "components": {
      "networkId": {"digits": 2, "format": "NUMERIC", "value": "01"},
      "facilityId": {"digits": 3, "format": "NUMERIC"},
      "sequence": {"digits": 8, "format": "SPLIT"},
      "separator": "-",
      "hospitalShortform": {"enabled": true, "value": "MED"}
    }
  }'
```

### Generate UPID
```bash
curl -X POST http://localhost:8080/api/admin/upid-config/1/generate/5
```
**Response**: `{"upid": "MED-005-01-0000-0001", "facilityId": "5", "configId": 1}`

### View All Configurations
```bash
curl -X GET http://localhost:8080/api/admin/upid-config
```

## **Data Storage Structure**

### Main Configuration Entry
```sql
INSERT INTO lookup_values (category, code, display_name, sort_order, active)
VALUES ('UPID_CONFIG', 'Custom Medical Center', 'Medical center format', 1, true);
```

### Configuration Components
```sql
INSERT INTO lookup_values (category, code, display_name, sort_order, active) VALUES
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'PATTERN_TEMPLATE', 'HOSPITAL-FACILITY-NETWORK-SEQUENCE', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'NETWORK_ID_DIGITS', '2', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'NETWORK_ID_VALUE', '01', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'FACILITY_ID_DIGITS', '3', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'FACILITY_ID_FORMAT', 'NUMERIC', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'SEQUENCE_DIGITS', '8', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'SEQUENCE_FORMAT', 'SPLIT', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'SEPARATOR', '-', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'HOSPITAL_SHORTFORM', 'MED', 0, true),
('UPID_CONFIG_a0000000-0000-0000-0000-000000000001', 'USE_HOSPITAL_SHORTFORM', 'true', 0, true);
```

### Sequence Counter
```sql
INSERT INTO lookup_values (category, code, display_name, sort_order, active)
VALUES ('UPID_SEQUENCE_COUNTER', '5_a0000000-0000-0000-0000-000000000001', '1', 0, true);
```

### History Entry
```sql
INSERT INTO lookup_values (category, code, display_name, sort_order, active)
VALUES ('UPID_CONFIG_HISTORY', 'a0000000-0000-0000-0000-000000000001_1672531200000', 
        'CREATE|ADMIN|New configuration created||{configName:Custom Medical Center}', 0, true);
```

## **Benefits of LookupValue Approach**

1. **No New Tables**: Uses existing infrastructure
2. **Consistent API**: Leverages existing LookupService
3. **Flexible Storage**: Easy to add new configuration options
4. **Audit Trail**: Built-in history tracking
5. **Performance**: Indexed queries on existing table
6. **Maintenance**: Single table to manage

## **Integration with Patient Registration**

The system seamlessly integrates with existing patient registration:

```kotlin
// In PatientService.kt
fun generateNextMrn(facilityId: String, lastMrn: String?): String {
    return try {
        advancedUpidConfigService.generateUpid(facilityId, null) // Uses default config
    } catch (e: Exception) {
        logger.warning("Failed to use advanced UPID configuration, falling back to legacy method")
        generateLegacyMrn(facilityId, lastMrn) // Fallback to existing method
    }
}
```

## **Testing**

Run the migration and test the API:

1. **Execute Migration**: `V5__Add_upid_configuration_lookup_values.sql`
2. **Test API**: Use the provided test script
3. **Verify Data**: Check lookup_values table for UPID categories
4. **Generate UPIDs**: Test UPID generation with different configurations

## **Next Steps**

1. **Run Migration**: Execute V5 migration to populate lookup_values
2. **Test Endpoints**: Use the API guide to test all functionality
3. **Configure Patterns**: Create organization-specific UPID patterns
4. **Monitor Usage**: Review generated UPIDs and configuration changes
5. **Train Users**: Provide API documentation to administrators

This implementation provides the complete UPID configuration system you requested using the existing LookupValue table, making it easier to maintain and integrate with your current infrastructure.

## **Sample Configuration Queries**

### View All UPID Configurations
```sql
SELECT * FROM lookup_values WHERE category = 'UPID_CONFIG' AND active = true ORDER BY sort_order, code;
```

### View Configuration Components
```sql
SELECT * FROM lookup_values WHERE category LIKE 'UPID_CONFIG_%' AND category != 'UPID_CONFIG' AND active = true;
```

### View Sequence Counters
```sql
SELECT * FROM lookup_values WHERE category = 'UPID_SEQUENCE_COUNTER' AND active = true;
```

### View Configuration History
```sql
SELECT * FROM lookup_values WHERE category = 'UPID_CONFIG_HISTORY' ORDER BY created_at DESC;
```

The system is now ready for use with full UPID configuration capabilities using your existing LookupValue infrastructure!
