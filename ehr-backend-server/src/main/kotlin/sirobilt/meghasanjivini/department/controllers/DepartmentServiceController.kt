package sirobilt.meghasanjivini.department.controllers


import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.department.dto.DepartmentServiceDTO
import sirobilt.meghasanjivini.department.model.DepartmentService
import sirobilt.meghasanjivini.department.service.DepartmentServiceService

@Path("/department-services")
@Tag(name = "Department Services", description = "Manage services provided by departments")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class DepartmentServiceController(
    private val service: DepartmentServiceService
) {

    @POST
    @Operation(summary = "Create a new department service")

    fun create(dto: DepartmentServiceDTO): Response {
        val created = service.create(dto)
        return Response.status(Response.Status.CREATED).entity(created).build()
    }

    @GET
    @Path("/{departmentId}")
    @Operation(summary = "Get all services for a department")

    fun getByDepartment(
        @Parameter(description = "Department ID") @PathParam("departmentId") departmentId: String
    ): List<DepartmentService> = service.getByDepartment(departmentId)
}
