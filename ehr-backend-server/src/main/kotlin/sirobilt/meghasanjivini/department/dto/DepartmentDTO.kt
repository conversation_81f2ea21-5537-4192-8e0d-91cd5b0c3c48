package sirobilt.meghasanjivini.department.dto

data class DepartmentDTO(
    val departmentId: String?,
    val facilityId: String?,
    var name: String,
    var code: String,
    var description: String?,
    var headOfDepartment: String?,
    var phoneNumber: String?,
    var email: String?,
    var location: String?,
    val operatingHours: List<DepartmentOperatingHoursDTO> = emptyList(),
    var isActive: Boolean = true,
    var isEmergencyDepartment: Boolean = false
)