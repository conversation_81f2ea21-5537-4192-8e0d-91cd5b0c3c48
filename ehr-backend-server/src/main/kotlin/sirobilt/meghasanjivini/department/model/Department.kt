package sirobilt.meghasanjivini.department.model

import com.fasterxml.jackson.annotation.JsonManagedReference
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import java.time.LocalDateTime
import java.util.*

@Entity
@Table(name = "departments")
class Department : PanacheEntityBase {

    @Id
    var departmentId: String = UUID.randomUUID().toString()
    var facilityId: String? = ""
    var name: String = ""
    var code: String = ""
    var description: String? = null
    var headOfDepartment: String? = null
    var phoneNumber: String? = null
    var email: String? = null
    var location: String? = null


    @JvmSuppressWildcards
    @OneToMany(
        mappedBy = "department",
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        orphanRemoval = true
    )
    @JsonManagedReference
    open var operatingHours: List<DepartmentOperatingHours> = mutableListOf()

    var isActive: Boolean = true
    var isEmergencyDepartment: Boolean = false
    var createdAt: LocalDateTime = LocalDateTime.now()
    var updatedAt: LocalDateTime = LocalDateTime.now()
    var createdBy: String? = null
    var updatedBy: String? = null
}
