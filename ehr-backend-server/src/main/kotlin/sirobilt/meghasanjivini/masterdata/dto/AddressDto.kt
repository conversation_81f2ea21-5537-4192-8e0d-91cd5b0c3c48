package sirobilt.meghasanjivini.masterdata.dto

import sirobilt.meghasanjivini.masterdata.model.Address

data class AddressDto(
    val street: String?,
    val city: String?,
    val state: String?,
    val zipCode: String?,
    val country: String?
) {
    companion object {
        fun fromEmbeddable(address: Address): AddressDto {
            return AddressDto(
                street = address.street,
                city = address.city,
                state = address.state,
                zipCode = address.zipCode,
                country = address.country
            )
        }
    }
}
