package sirobilt.meghasanjivini.masterdata.dto


import sirobilt.meghasanjivini.masterdata.model.Doctor
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

data class ListDoctorDto(
    val doctorId: UUID, // references specialization_id
    val fullName: String,
    val gender: String,
    val dateOfBirth: LocalDate,
    val mobileNumber: String,
    val email: String,
    val registrationNumber: String,
    val registrationState: String?,
    val yearsOfExperience: Int,
    val telemedicineReady: Boolean,
    val languagesSpoken: List<String>,
    val isActive: Boolean,
    val address: AddressDto,
    val createdAt: LocalDateTime? = null
) {
    companion object {
        fun fromEntity(doc: Doctor): ListDoctorDto {
            return ListDoctorDto(
                doctorId = doc.doctorId,
                fullName = doc.fullName,
                gender = doc.gender,
                dateOfBirth = doc.dateOfBirth,
                mobileNumber = doc.mobileNumber,
                email = doc.email,
                registrationNumber = doc.registrationNumber,
                registrationState = doc.registrationState,
                yearsOfExperience = doc.yearsOfExperience,
                telemedicineReady = doc.telemedicineReady,
                languagesSpoken = doc.languagesSpoken,
                isActive = doc.isActive,
                address = AddressDto.fromEmbeddable(doc.address),
                createdAt = doc.createdAt
            )
        }
    }
}
