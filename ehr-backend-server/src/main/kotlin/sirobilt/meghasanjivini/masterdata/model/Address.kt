package sirobilt.meghasanjivini.masterdata.model

import jakarta.persistence.Column
import jakarta.persistence.Embeddable

@Embeddable
class Address {
    @Column(name = "street", length = 100)
    var street: String? = null

    @Column(name = "city", length = 50)
    var city: String? = null

    @Column(name = "state", length = 50)
    var state: String? = null

    @Column(name = "zip_code", length = 10)
    var zipCode: String? = null

    @Column(name = "country", length = 50)
    var country: String? = null
}
