package sirobilt.meghasanjivini.patientregistration.controller

import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.patientregistration.service.UpidConfigurationService
import sirobilt.meghasanjivini.patientregistration.service.AdvancedUpidConfigurationService
import sirobilt.meghasanjivini.patientregistration.dto.*
import java.time.OffsetDateTime
import java.util.logging.Logger

/**
 * REST Controller for UPID (Unique Patient ID) configuration management
 */
@Path("/upid-config")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "UPID Configuration", description = "Unique Patient ID configuration and generation APIs")
class UpidConfigurationController {

    @Inject
    lateinit var upidConfigService: UpidConfigurationService

    private val logger: Logger = Logger.getLogger(UpidConfigurationController::class.java.name)

    /**
     * Get current UPID configuration
     */
    @GET
    @Path("/current")
    @Operation(summary = "Get current UPID configuration", description = "Get the current UPID format configuration")
    fun getCurrentConfiguration(): Response {
        return try {
            val config = upidConfigService.getCurrentConfiguration()
            Response.ok(config).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve UPID configuration"))
                .build()
        }
    }

    /**
     * Get available UPID formats
     */
    @GET
    @Path("/formats")
    @Operation(summary = "Get available UPID formats", description = "Get list of available UPID format types")
    fun getAvailableFormats(): Response {
        return try {
            val formats = upidConfigService.getAvailableFormats()
            Response.ok(formats).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving UPID formats: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve UPID formats"))
                .build()
        }
    }

    /**
     * UPID configuration health check
     */
    @GET
    @Path("/health")
    @Operation(summary = "UPID configuration health check", description = "Check if UPID configuration is healthy")
    fun healthCheck(): Response {
        return try {
            val health = upidConfigService.healthCheck()
            Response.ok(health).build()
        } catch (e: Exception) {
            logger.severe("UPID health check failed: ${e.message}")
            Response.status(Response.Status.SERVICE_UNAVAILABLE)
                .entity(mapOf("error" to "UPID configuration health check failed"))
                .build()
        }
    }

    /**
     * Generate sample UPID for a facility
     */
    @POST
    @Path("/generate-sample/{facilityId}")
    @Operation(summary = "Generate sample UPID", description = "Generate a sample UPID for the specified facility")
    fun generateSampleUpid(@PathParam("facilityId") facilityId: String): Response {
        return try {
            val sampleUpid = upidConfigService.generateSampleUpid(facilityId)
            Response.ok(mapOf("upid" to sampleUpid, "facilityId" to facilityId)).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid facility ID for UPID generation: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error generating sample UPID: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to generate sample UPID"))
                .build()
        }
    }

    /**
     * Validate UPID format
     */
    @POST
    @Path("/validate")
    @Operation(summary = "Validate UPID format", description = "Validate if a UPID follows the correct format")
    fun validateUpid(@Valid request: UpidValidationRequest): Response {
        return try {
            val validation = upidConfigService.validateUpid(request.upid)
            Response.ok(validation).build()
        } catch (e: Exception) {
            logger.severe("Error validating UPID: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to validate UPID"))
                .build()
        }
    }

    /**
     * Get effective configuration (Admin endpoint)
     */
    @GET
    @Path("/admin/effective")
    @Operation(summary = "Get effective configuration", description = "Get the effective UPID configuration (admin)")
    fun getEffectiveConfiguration(): Response {
        return try {
            val config = upidConfigService.getEffectiveConfiguration()
            Response.ok(config).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving effective UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve effective configuration"))
                .build()
        }
    }

    /**
     * Get configuration history (Admin endpoint)
     */
    @GET
    @Path("/admin/history")
    @Operation(summary = "Get configuration history", description = "Get UPID configuration change history")
    fun getConfigurationHistory(): Response {
        return try {
            val history = upidConfigService.getConfigurationHistory()
            Response.ok(history).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving UPID configuration history: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve configuration history"))
                .build()
        }
    }

    /**
     * Validate configuration changes (Admin endpoint)
     */
    @POST
    @Path("/admin/validate")
    @Operation(summary = "Validate configuration changes", description = "Validate UPID configuration changes without applying them")
    fun validateConfiguration(@Valid request: UpidConfigValidationRequest): Response {
        return try {
            val validation = upidConfigService.validateConfiguration(request)
            Response.ok(validation).build()
        } catch (e: Exception) {
            logger.severe("Error validating UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to validate configuration"))
                .build()
        }
    }

    /**
     * Generate test samples with custom configuration (Admin endpoint)
     */
    @POST
    @Path("/admin/test-samples/{facilityId}")
    @Operation(summary = "Generate test samples", description = "Generate test UPID samples with custom configuration")
    fun generateTestSamples(
        @PathParam("facilityId") facilityId: String,
        @Valid request: UpidTestSampleRequest
    ): Response {
        return try {
            val samples = upidConfigService.generateTestSamples(facilityId, request)
            Response.ok(samples).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid request for test samples: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error generating test samples: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to generate test samples"))
                .build()
        }
    }
}

/**
 * Advanced UPID Configuration Controller for Admin APIs
 * Supports regex-based pattern configuration with configurable components
 */
@Path("/admin/upid-config")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Advanced UPID Configuration", description = "Advanced UPID configuration management with regex patterns")
class AdvancedUpidConfigurationController {

    @Inject
    lateinit var advancedUpidConfigService: AdvancedUpidConfigurationService

    private val logger: Logger = Logger.getLogger(AdvancedUpidConfigurationController::class.java.name)

    /**
     * Create new UPID configuration
     */
    @POST
    @Operation(summary = "Create UPID configuration", description = "Create a new UPID configuration with regex patterns")
    fun createConfiguration(@Valid request: CreateUpidConfigurationRequest): Response {
        return try {
            val config = advancedUpidConfigService.createConfiguration(request, "ADMIN")
            Response.status(Response.Status.CREATED).entity(config).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid request for creating configuration: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error creating UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to create configuration"))
                .build()
        }
    }

    /**
     * Update existing UPID configuration
     */
    @PUT
    @Path("/{configId}")
    @Operation(summary = "Update UPID configuration", description = "Update an existing UPID configuration")

    fun updateConfiguration(
        @PathParam("configId") configId: Long,
        @Valid request: UpdateUpidConfigurationRequest
    ): Response {
        return try {
            val config = advancedUpidConfigService.updateConfiguration(configId, request, "ADMIN")
            Response.ok(config).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid request for updating configuration: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error updating UPID configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to update configuration"))
                .build()
        }
    }

    /**
     * Get all UPID configurations
     */
    @GET
    @Operation(summary = "Get all configurations", description = "Get all active UPID configurations")
    @APIResponse(responseCode = "200", description = "Configurations retrieved successfully")
    fun getAllConfigurations(): Response {
        return try {
            val configurations = advancedUpidConfigService.getAllConfigurations()
            Response.ok(configurations).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving configurations: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve configurations"))
                .build()
        }
    }

    /**
     * Get UPID configuration by ID
     */
    @GET
    @Path("/{configId}")
    @Operation(summary = "Get configuration by ID", description = "Get UPID configuration by ID")
   fun getConfigurationById(@PathParam("configId") configId: Long): Response {
        return try {
            val configuration = advancedUpidConfigService.getConfigurationById(configId)
            Response.ok(configuration).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Configuration not found: ${e.message}")
            Response.status(Response.Status.NOT_FOUND)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error retrieving configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve configuration"))
                .build()
        }
    }

    /**
     * Get default UPID configuration
     */
    @GET
    @Path("/default")
    @Operation(summary = "Get default configuration", description = "Get the default UPID configuration")
   fun getDefaultConfiguration(): Response {
        return try {
            val configuration = advancedUpidConfigService.getDefaultConfiguration()
            Response.ok(configuration).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Default configuration not found: ${e.message}")
            Response.status(Response.Status.NOT_FOUND)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error retrieving default configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve default configuration"))
                .build()
        }
    }

    /**
     * Delete UPID configuration
     */
    @DELETE
    @Path("/{configId}")
    @Operation(summary = "Delete configuration", description = "Delete UPID configuration (soft delete)")
   fun deleteConfiguration(@PathParam("configId") configId: Long): Response {
        return try {
            val success = advancedUpidConfigService.deleteConfiguration(configId, "ADMIN")
            if (success) {
                Response.noContent().build()
            } else {
                Response.status(Response.Status.NOT_FOUND)
                    .entity(mapOf("error" to "Configuration not found"))
                    .build()
            }
        } catch (e: IllegalArgumentException) {
            logger.warning("Cannot delete configuration: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error deleting configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to delete configuration"))
                .build()
        }
    }

    /**
     * Set configuration as default
     */
    @POST
    @Path("/{configId}/set-default")
    @Operation(summary = "Set as default", description = "Set UPID configuration as default")
   fun setAsDefault(@PathParam("configId") configId: Long): Response {
        return try {
            val success = advancedUpidConfigService.setAsDefault(configId, "ADMIN")
            if (success) {
                Response.ok(mapOf("message" to "Configuration set as default successfully")).build()
            } else {
                Response.status(Response.Status.NOT_FOUND)
                    .entity(mapOf("error" to "Configuration not found"))
                    .build()
            }
        } catch (e: IllegalArgumentException) {
            logger.warning("Cannot set configuration as default: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error setting configuration as default: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to set configuration as default"))
                .build()
        }
    }

    /**
     * Generate UPID using specific configuration
     */
    @POST
    @Path("/{configId}/generate/{facilityId}")
    @Operation(summary = "Generate UPID", description = "Generate UPID using specific configuration")
   fun generateUpid(
        @PathParam("configId") configId: Long,
        @PathParam("facilityId") facilityId: String
    ): Response {
        return try {
            val upid = advancedUpidConfigService.generateUpid(facilityId, configId)
            Response.ok(mapOf(
                "upid" to upid,
                "facilityId" to facilityId,
                "configId" to configId,
                "timestamp" to OffsetDateTime.now()
            )).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Cannot generate UPID: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error generating UPID: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to generate UPID"))
                .build()
        }
    }

    /**
     * Validate UPID pattern
     */
    @POST
    @Path("/validate-pattern")
    @Operation(summary = "Validate pattern", description = "Validate UPID pattern and generate samples")
   fun validatePattern(@Valid request: UpidPatternValidationRequest): Response {
        return try {
            val validation = advancedUpidConfigService.validatePattern(request)
            Response.ok(validation).build()
        } catch (e: Exception) {
            logger.severe("Error validating pattern: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to validate pattern"))
                .build()
        }
    }

    /**
     * Get configuration history
     */
    @GET
    @Path("/{configId}/history")
    @Operation(summary = "Get configuration history", description = "Get change history for UPID configuration")
   fun getConfigurationHistory(@PathParam("configId") configId: Long): Response {
        return try {
            val history = advancedUpidConfigService.getConfigurationHistory(configId)
            Response.ok(history).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving configuration history: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve configuration history"))
                .build()
        }
    }
}

/**
 * Request DTOs for UPID validation and configuration
 */
data class UpidValidationRequest(
    val upid: String
)

data class UpidConfigValidationRequest(
    val changes: Map<String, String>,
    val validateOnly: Boolean = true
)

data class UpidTestSampleRequest(
    val facilityId: String,
    val sampleCount: Int = 3,
    val tempConfig: Map<String, String>? = null
)
