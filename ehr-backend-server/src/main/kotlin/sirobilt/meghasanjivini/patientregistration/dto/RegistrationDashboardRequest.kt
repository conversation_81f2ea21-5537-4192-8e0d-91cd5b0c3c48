package sirobilt.meghasanjivini.patientregistration.dto

import com.fasterxml.jackson.annotation.JsonFormat
import sirobilt.meghasanjivini.patientregistration.model.Granularity
import java.time.LocalDate

data class RegistrationDashboardRequest(
    @field:JsonFormat(pattern = "yyyy-MM-dd")
    val fromDate: LocalDate,
    @field:JsonFormat(pattern = "yyyy-MM-dd")
    val toDate: LocalDate,
    val granularity: Granularity
)