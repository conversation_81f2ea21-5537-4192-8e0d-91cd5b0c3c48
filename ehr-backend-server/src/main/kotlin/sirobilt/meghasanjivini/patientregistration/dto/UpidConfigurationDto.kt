package sirobilt.meghasanjivini.patientregistration.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.*
import java.time.OffsetDateTime

/**
 * DTO for UPID configuration requests and responses
 */
data class UpidConfigurationDto(
    @JsonProperty("configId")
    val configId: Long? = null,

    @JsonProperty("configName")
    @field:NotBlank(message = "Configuration name is required")
    @field:Size(max = 100, message = "Configuration name must not exceed 100 characters")
    val configName: String,

    @JsonProperty("patternRegex")
    @field:NotBlank(message = "Pattern regex is required")
    @field:Size(max = 500, message = "Pattern regex must not exceed 500 characters")
    val patternRegex: String,

    @JsonProperty("patternTemplate")
    @field:NotBlank(message = "Pattern template is required")
    @field:Size(max = 200, message = "Pattern template must not exceed 200 characters")
    val patternTemplate: String,

    @JsonProperty("description")
    @field:Size(max = 500, message = "Description must not exceed 500 characters")
    val description: String? = null,

    // Component configurations
    @JsonProperty("networkIdDigits")
    @field:Min(value = 1, message = "Network ID digits must be at least 1")
    @field:Max(value = 5, message = "Network ID digits must not exceed 5")
    val networkIdDigits: Int = 2,

    @JsonProperty("networkIdValue")
    @field:Size(max = 10, message = "Network ID value must not exceed 10 characters")
    val networkIdValue: String = "00",

    @JsonProperty("facilityIdDigits")
    @field:Min(value = 1, message = "Facility ID digits must be at least 1")
    @field:Max(value = 10, message = "Facility ID digits must not exceed 10")
    val facilityIdDigits: Int = 3,

    @JsonProperty("facilityIdFormat")
    val facilityIdFormat: FacilityIdFormat = FacilityIdFormat.NUMERIC,

    @JsonProperty("sequenceDigits")
    @field:Min(value = 4, message = "Sequence digits must be at least 4")
    @field:Max(value = 12, message = "Sequence digits must not exceed 12")
    val sequenceDigits: Int = 8,

    @JsonProperty("sequenceFormat")
    val sequenceFormat: SequenceFormat = SequenceFormat.SPLIT,

    @JsonProperty("separator")
    @field:Size(max = 5, message = "Separator must not exceed 5 characters")
    val separator: String = "-",

    @JsonProperty("hospitalShortform")
    @field:Size(max = 20, message = "Hospital shortform must not exceed 20 characters")
    val hospitalShortform: String? = null,

    @JsonProperty("useHospitalShortform")
    val useHospitalShortform: Boolean = false,

    @JsonProperty("examplePattern")
    val examplePattern: String = "",

    @JsonProperty("validationRegex")
    val validationRegex: String = "",

    @JsonProperty("isActive")
    val isActive: Boolean = true,

    @JsonProperty("isDefault")
    val isDefault: Boolean = false,

    @JsonProperty("createdBy")
    val createdBy: String? = null,

    @JsonProperty("updatedBy")
    val updatedBy: String? = null,

    @JsonProperty("createdAt")
    val createdAt: OffsetDateTime? = null,

    @JsonProperty("updatedAt")
    val updatedAt: OffsetDateTime? = null,

    @JsonProperty("version")
    val version: Long? = null
)

/**
 * DTO for creating new UPID configuration
 */
data class CreateUpidConfigurationRequest(
    @JsonProperty("configName")
    @field:NotBlank(message = "Configuration name is required")
    val configName: String,

    @JsonProperty("patternTemplate")
    @field:NotBlank(message = "Pattern template is required")
    val patternTemplate: String,

    @JsonProperty("description")
    val description: String? = null,

    @JsonProperty("components")
    val components: UpidComponentsDto,

    @JsonProperty("isDefault")
    val isDefault: Boolean = false
)

/**
 * DTO for updating UPID configuration
 */
data class UpdateUpidConfigurationRequest(
    @JsonProperty("configName")
    val configName: String? = null,

    @JsonProperty("patternTemplate")
    val patternTemplate: String? = null,

    @JsonProperty("description")
    val description: String? = null,

    @JsonProperty("components")
    val components: UpidComponentsDto? = null,

    @JsonProperty("isActive")
    val isActive: Boolean? = null,

    @JsonProperty("isDefault")
    val isDefault: Boolean? = null,

    @JsonProperty("changeReason")
    val changeReason: String? = null
)

/**
 * DTO for UPID component configuration
 */
data class UpidComponentsDto(
    @JsonProperty("networkId")
    val networkId: ComponentConfigDto,

    @JsonProperty("facilityId")
    val facilityId: ComponentConfigDto,

    @JsonProperty("sequence")
    val sequence: ComponentConfigDto,

    @JsonProperty("separator")
    @field:Size(max = 5, message = "Separator must not exceed 5 characters")
    val separator: String = "-",

    @JsonProperty("hospitalShortform")
    val hospitalShortform: HospitalShortformDto? = null
)

/**
 * DTO for individual component configuration
 */
data class ComponentConfigDto(
    @JsonProperty("digits")
    @field:Min(value = 1, message = "Digits must be at least 1")
    @field:Max(value = 12, message = "Digits must not exceed 12")
    val digits: Int,

    @JsonProperty("format")
    val format: String,

    @JsonProperty("value")
    val value: String? = null,

    @JsonProperty("padding")
    val padding: String = "0"
)

/**
 * DTO for hospital shortform configuration
 */
data class HospitalShortformDto(
    @JsonProperty("enabled")
    val enabled: Boolean = false,

    @JsonProperty("value")
    @field:Size(max = 20, message = "Hospital shortform must not exceed 20 characters")
    val value: String? = null,

    @JsonProperty("position")
    val position: ShortformPosition = ShortformPosition.PREFIX
)

/**
 * DTO for UPID pattern validation
 */
data class UpidPatternValidationRequest(
    @JsonProperty("patternTemplate")
    @field:NotBlank(message = "Pattern template is required")
    val patternTemplate: String,

    @JsonProperty("components")
    val components: UpidComponentsDto,

    @JsonProperty("testFacilityId")
    val testFacilityId: String = "1",

    @JsonProperty("sampleCount")
    @field:Min(value = 1, message = "Sample count must be at least 1")
    @field:Max(value = 10, message = "Sample count must not exceed 10")
    val sampleCount: Int = 3
)

/**
 * DTO for UPID pattern validation response
 */
data class UpidPatternValidationResponse(
    @JsonProperty("valid")
    val valid: Boolean,

    @JsonProperty("errors")
    val errors: List<String> = emptyList(),

    @JsonProperty("warnings")
    val warnings: List<String> = emptyList(),

    @JsonProperty("generatedRegex")
    val generatedRegex: String? = null,

    @JsonProperty("validationRegex")
    val validationRegex: String? = null,

    @JsonProperty("sampleUpids")
    val sampleUpids: List<String> = emptyList(),

    @JsonProperty("patternBreakdown")
    val patternBreakdown: Map<String, String> = emptyMap(),

    @JsonProperty("timestamp")
    val timestamp: OffsetDateTime = OffsetDateTime.now()
)

/**
 * Enums for UPID configuration
 */
enum class FacilityIdFormat {
    NUMERIC, ALPHA, ALPHANUMERIC
}

enum class SequenceFormat {
    SPLIT, CONTINUOUS
}

enum class ShortformPosition {
    PREFIX, SUFFIX, MIDDLE
}
