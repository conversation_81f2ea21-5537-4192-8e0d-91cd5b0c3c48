package sirobilt.meghasanjivini.patientregistration.model

import jakarta.persistence.*
import java.time.OffsetDateTime

/**
 * Entity for storing UPID (Unique Patient ID) configuration
 */
@Entity
@Table(name = "upid_configuration")
class UpidConfigurationEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "config_id")
    var configId: Long = 0,

    @Column(name = "config_name", unique = true, nullable = false, length = 100)
    var configName: String = "",

    @Column(name = "pattern_regex", nullable = false, length = 500)
    var patternRegex: String = "",

    @Column(name = "pattern_template", nullable = false, length = 200)
    var patternTemplate: String = "",

    @Column(name = "description")
    var description: String? = null,

    @Column(name = "network_id_digits")
    var networkIdDigits: Int = 2,

    @Column(name = "network_id_value", length = 10)
    var networkIdValue: String = "00",

    @Column(name = "facility_id_digits")
    var facilityIdDigits: Int = 3,

    @Column(name = "facility_id_format", length = 50)
    var facilityIdFormat: String = "NUMERIC",

    @Column(name = "sequence_digits")
    var sequenceDigits: Int = 8,

    @Column(name = "sequence_format", length = 50)
    var sequenceFormat: String = "SPLIT",

    @Column(name = "separator", length = 5)
    var separator: String = "-",

    @Column(name = "hospital_shortform", length = 20)
    var hospitalShortform: String? = null,

    @Column(name = "use_hospital_shortform")
    var useHospitalShortform: Boolean = false,

    @Column(name = "example_pattern", length = 100)
    var examplePattern: String = "",

    @Column(name = "validation_regex", length = 500)
    var validationRegex: String = "",

    @Column(name = "is_active")
    var isActive: Boolean = true,

    @Column(name = "is_default")
    var isDefault: Boolean = false,

    @Column(name = "created_by", length = 100)
    var createdBy: String = "SYSTEM",

    @Column(name = "updated_by", length = 100)
    var updatedBy: String = "SYSTEM",

    @Column(name = "created_at")
    var createdAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "updated_at")
    var updatedAt: OffsetDateTime = OffsetDateTime.now(),

    @Version
    @Column(name = "version")
    var version: Long = 0
) {
    override fun toString(): String = "UpidConfiguration(id=$configId, name=$configName, pattern=$patternTemplate)"
}

/**
 * Entity for UPID configuration history
 */
@Entity
@Table(name = "upid_configuration_history")
class UpidConfigurationHistoryEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "history_id")
    var historyId: Long = 0,

    @Column(name = "config_id")
    var configId: Long = 0,

    @Column(name = "action", length = 50)
    var action: String = "",

    @Column(name = "old_values", columnDefinition = "TEXT")
    var oldValues: String? = null,

    @Column(name = "new_values", columnDefinition = "TEXT")
    var newValues: String? = null,

    @Column(name = "changed_by", length = 100)
    var changedBy: String = "",

    @Column(name = "change_reason", length = 500)
    var changeReason: String? = null,

    @Column(name = "created_at")
    var createdAt: OffsetDateTime = OffsetDateTime.now()
) {
    override fun toString(): String = "UpidConfigHistory(id=$historyId, configId=$configId, action=$action)"
}

/**
 * Entity for UPID sequence counters per facility
 */
@Entity
@Table(
    name = "upid_sequence_counter",
    uniqueConstraints = [UniqueConstraint(columnNames = ["facility_id", "config_id"])]
)
class UpidSequenceCounterEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "counter_id")
    var counterId: Long = 0,

    @Column(name = "facility_id", nullable = false, length = 20)
    var facilityId: String = "",

    @Column(name = "config_id")
    var configId: Long = 0,

    @Column(name = "current_sequence")
    var currentSequence: Long = 0,

    @Column(name = "last_generated_upid", length = 100)
    var lastGeneratedUpid: String? = null,

    @Column(name = "created_at")
    var createdAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "updated_at")
    var updatedAt: OffsetDateTime = OffsetDateTime.now()
) {
    override fun toString(): String = "UpidSequenceCounter(facilityId=$facilityId, sequence=$currentSequence)"
}
