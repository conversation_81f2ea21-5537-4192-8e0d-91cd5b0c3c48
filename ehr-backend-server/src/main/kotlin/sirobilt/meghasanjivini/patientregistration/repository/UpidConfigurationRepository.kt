package sirobilt.meghasanjivini.patientregistration.repository


import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.model.UpidConfigurationEntity
import sirobilt.meghasanjivini.patientregistration.model.UpidConfigurationHistoryEntity
import sirobilt.meghasanjivini.patientregistration.model.UpidSequenceCounterEntity
import java.nio.file.Files.find
import java.time.OffsetDateTime

/**
 * Repository for UPID configuration management
 */
@ApplicationScoped
class UpidConfigurationRepository : PanacheRepositoryBase<UpidConfigurationEntity, Long> {

    /**
     * Find active configuration by name
     */
    fun findActiveByName(configName: String): UpidConfigurationEntity? {
        return find("configName = ?1 AND isActive = true", configName).firstResult()
    }

    /**
     * Find default active configuration
     */
    fun findDefaultActive(): UpidConfigurationEntity? {
        return find("isDefault = true AND isActive = true").firstResult()
    }

    /**
     * Find all active configurations
     */
    fun findAllActive(): List<UpidConfigurationEntity> {
        return find("isActive = true ORDER BY configName").list()
    }

    /**
     * Find all configurations with pagination
     */
    fun findAllWithPagination(page: Int, size: Int): List<UpidConfigurationEntity> {
        return findAll().page(page, size).list()
    }

    /**
     * Check if configuration name exists (excluding specific ID)
     */
    fun existsByNameExcludingId(configName: String, excludeId: Long?): Boolean {
        return if (excludeId != null) {
            count("configName = ?1 AND configId != ?2", configName, excludeId) > 0
        } else {
            count("configName = ?1", configName) > 0
        }
    }

    /**
     * Deactivate all default configurations
     */
    @Transactional
    fun deactivateAllDefaults(): Int {
        return update("isDefault = false WHERE isDefault = true")
    }

    /**
     * Set configuration as default
     */
    @Transactional
    fun setAsDefault(configId: Long): Boolean {
        // First deactivate all defaults
        deactivateAllDefaults()
        // Then set the specified config as default
        return update("isDefault = true WHERE configId = ?1 AND isActive = true", configId) > 0
    }

    /**
     * Soft delete configuration
     */
    @Transactional
    fun softDelete(configId: Long): Boolean {
        return update("isActive = false, updatedAt = ?1 WHERE configId = ?2", OffsetDateTime.now(), configId) > 0
    }

    /**
     * Update configuration timestamps
     */
    @Transactional
    fun updateTimestamp(configId: Long, updatedBy: String): Boolean {
        return update("updatedAt = ?1, updatedBy = ?2 WHERE configId = ?3", 
                     OffsetDateTime.now(), updatedBy, configId) > 0
    }
}

/**
 * Repository for UPID configuration history
 */
@ApplicationScoped
class UpidConfigurationHistoryRepository : PanacheRepositoryBase<UpidConfigurationHistoryEntity, Long> {

    /**
     * Find history by configuration ID
     */
    fun findByConfigId(configId: Long): List<UpidConfigurationHistoryEntity> {
        return find("configId = ?1 ORDER BY createdAt DESC", configId).list()
    }

    /**
     * Find recent history entries
     */
    fun findRecentHistory(limit: Int = 50): List<UpidConfigurationHistoryEntity> {
        return find("ORDER BY createdAt DESC").page(0, limit).list()
    }

    /**
     * Find history by action type
     */
    fun findByAction(action: String): List<UpidConfigurationHistoryEntity> {
        return find("action = ?1 ORDER BY createdAt DESC", action).list()
    }

    /**
     * Find history by user
     */
    fun findByUser(changedBy: String): List<UpidConfigurationHistoryEntity> {
        return find("changedBy = ?1 ORDER BY createdAt DESC", changedBy).list()
    }

    /**
     * Create history entry
     */
    @Transactional
    fun createHistoryEntry(
        configId: Long,
        action: String,
        oldValues: String?,
        newValues: String?,
        changedBy: String,
        changeReason: String?
    ): UpidConfigurationHistoryEntity {
        val historyEntry = UpidConfigurationHistoryEntity(
            configId = configId,
            action = action,
            oldValues = oldValues,
            newValues = newValues,
            changedBy = changedBy,
            changeReason = changeReason
        )
        persist(historyEntry)
        return historyEntry
    }
}

/**
 * Repository for UPID sequence counters
 */
@ApplicationScoped
class UpidSequenceCounterRepository : PanacheRepositoryBase<UpidSequenceCounterEntity, Long> {

    /**
     * Find counter by facility and config
     */
    fun findByFacilityAndConfig(facilityId: String, configId: Long): UpidSequenceCounterEntity? {
        return find("facilityId = ?1 AND configId = ?2", facilityId, configId).firstResult()
    }

    /**
     * Find all counters for a facility
     */
    fun findByFacility(facilityId: String): List<UpidSequenceCounterEntity> {
        return find("facilityId = ?1 ORDER BY configId", facilityId).list()
    }

    /**
     * Find all counters for a configuration
     */
    fun findByConfig(configId: Long): List<UpidSequenceCounterEntity> {
        return find("configId = ?1 ORDER BY facilityId", configId).list()
    }

    /**
     * Get next sequence number for facility and config
     */
    @Transactional
    fun getNextSequence(facilityId: String, configId: Long): Long {
        val counter = findByFacilityAndConfig(facilityId, configId)
        
        return if (counter != null) {
            counter.currentSequence += 1
            counter.updatedAt = OffsetDateTime.now()
            persist(counter)
            counter.currentSequence
        } else {
            // Create new counter starting from 1
            val newCounter = UpidSequenceCounterEntity(
                facilityId = facilityId,
                configId = configId,
                currentSequence = 1
            )
            persist(newCounter)
            1L
        }
    }

    /**
     * Update last generated UPID
     */
    @Transactional
    fun updateLastGeneratedUpid(facilityId: String, configId: Long, upid: String): Boolean {
        return update(
            "lastGeneratedUpid = ?1, updatedAt = ?2 WHERE facilityId = ?3 AND configId = ?4",
            upid, OffsetDateTime.now(), facilityId, configId
        ) > 0
    }

    /**
     * Reset sequence counter for facility and config
     */
    @Transactional
    fun resetSequence(facilityId: String, configId: Long, newSequence: Long = 0): Boolean {
        return update(
            "currentSequence = ?1, updatedAt = ?2 WHERE facilityId = ?3 AND configId = ?4",
            newSequence, OffsetDateTime.now(), facilityId, configId
        ) > 0
    }

    /**
     * Get current sequence without incrementing
     */
    fun getCurrentSequence(facilityId: String, configId: Long): Long {
        val counter = findByFacilityAndConfig(facilityId, configId)
        return counter?.currentSequence ?: 0L
    }
}
