package sirobilt.meghasanjivini.patientregistration.service

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.masterdata.repository.LookupValueRepository
import sirobilt.meghasanjivini.masterdata.model.LookupValue
import sirobilt.meghasanjivini.masterdata.service.LookupService
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.OffsetDateTime
import java.time.Instant
import java.util.logging.Logger
import java.util.UUID

/**
 * Advanced service for managing UPID configuration using LookupValue table
 * Supports regex patterns and configurable components stored as lookup values
 */
@ApplicationScoped
class AdvancedUpidConfigurationService {

    @Inject
    lateinit var lookupValueRepository: LookupValueRepository

    @Inject
    lateinit var lookupService: LookupService

    @Inject
    lateinit var patientRepository: PatientRepository

    @Inject
    lateinit var objectMapper: ObjectMapper

    private val logger: Logger = Logger.getLogger(AdvancedUpidConfigurationService::class.java.name)

    // UPID Configuration Categories in LookupValue table
    companion object {
        const val UPID_CONFIG_CATEGORY = "UPID_CONFIG"
        const val UPID_NETWORK_ID_CATEGORY = "UPID_NETWORK_ID"
        const val UPID_FACILITY_FORMAT_CATEGORY = "UPID_FACILITY_FORMAT"
        const val UPID_SEQUENCE_FORMAT_CATEGORY = "UPID_SEQUENCE_FORMAT"
        const val UPID_HOSPITAL_SHORTFORM_CATEGORY = "UPID_HOSPITAL_SHORTFORM"
        const val UPID_SEQUENCE_COUNTER_CATEGORY = "UPID_SEQUENCE_COUNTER"

        // Configuration keys
        const val DEFAULT_CONFIG_KEY = "DEFAULT_CONFIG"
        const val PATTERN_TEMPLATE_KEY = "PATTERN_TEMPLATE"
        const val NETWORK_ID_DIGITS_KEY = "NETWORK_ID_DIGITS"
        const val NETWORK_ID_VALUE_KEY = "NETWORK_ID_VALUE"
        const val FACILITY_ID_DIGITS_KEY = "FACILITY_ID_DIGITS"
        const val FACILITY_ID_FORMAT_KEY = "FACILITY_ID_FORMAT"
        const val SEQUENCE_DIGITS_KEY = "SEQUENCE_DIGITS"
        const val SEQUENCE_FORMAT_KEY = "SEQUENCE_FORMAT"
        const val SEPARATOR_KEY = "SEPARATOR"
        const val HOSPITAL_SHORTFORM_KEY = "HOSPITAL_SHORTFORM"
        const val USE_HOSPITAL_SHORTFORM_KEY = "USE_HOSPITAL_SHORTFORM"
    }

    /**
     * Create new UPID configuration using LookupValue table
     */
    @Transactional
    fun createConfiguration(request: CreateUpidConfigurationRequest, createdBy: String = "ADMIN"): UpidConfigurationDto {
        logger.info("Creating new UPID configuration: ${request.configName}")

        // Check if configuration name already exists
        val existingConfig = lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true",
                                                        UPID_CONFIG_CATEGORY, request.configName).firstResult()
        if (existingConfig != null) {
            throw IllegalArgumentException("Configuration name '${request.configName}' already exists")
        }

        // Generate regex pattern from template
        val patternInfo = generatePatternFromTemplate(request.patternTemplate, request.components)

        // If this is set as default, deactivate other defaults
        if (request.isDefault) {
            deactivateAllDefaults()
        }

        // Create main configuration entry
        val configId = UUID.randomUUID()
        val mainConfig = LookupValue(
            id = configId,
            category = UPID_CONFIG_CATEGORY,
            code = request.configName,
            displayName = request.description ?: request.configName,
            sortOrder = if (request.isDefault) 1 else 99,
            active = true
        )
        lookupValueRepository.persist(mainConfig)

        // Store configuration components as lookup values
        saveConfigurationComponent(configId, PATTERN_TEMPLATE_KEY, request.patternTemplate)
        saveConfigurationComponent(configId, NETWORK_ID_DIGITS_KEY, request.components.networkId.digits.toString())
        saveConfigurationComponent(configId, NETWORK_ID_VALUE_KEY, request.components.networkId.value ?: "00")
        saveConfigurationComponent(configId, FACILITY_ID_DIGITS_KEY, request.components.facilityId.digits.toString())
        saveConfigurationComponent(configId, FACILITY_ID_FORMAT_KEY, request.components.facilityId.format)
        saveConfigurationComponent(configId, SEQUENCE_DIGITS_KEY, request.components.sequence.digits.toString())
        saveConfigurationComponent(configId, SEQUENCE_FORMAT_KEY, request.components.sequence.format)
        saveConfigurationComponent(configId, SEPARATOR_KEY, request.components.separator)

        request.components.hospitalShortform?.let { shortform ->
            saveConfigurationComponent(configId, HOSPITAL_SHORTFORM_KEY, shortform.value ?: "")
            saveConfigurationComponent(configId, USE_HOSPITAL_SHORTFORM_KEY, shortform.enabled.toString())
        }

        // Create history entry
        createHistoryEntry(configId, "CREATE", null, objectMapper.writeValueAsString(request), createdBy, "New configuration created")

        logger.info("UPID configuration created successfully: $configId")
        return buildConfigurationDto(configId, request.configName)
    }

    /**
     * Save configuration component as lookup value
     */
    private fun saveConfigurationComponent(configId: UUID, key: String, value: String) {
        val component = LookupValue(
            category = "${UPID_CONFIG_CATEGORY}_${configId}",
            code = key,
            displayName = value,
            sortOrder = 0,
            active = true
        )
        lookupValueRepository.persist(component)
    }

    /**
     * Get configuration component value
     */
    private fun getConfigurationComponent(configId: UUID, key: String): String? {
        return lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true",
                                         "${UPID_CONFIG_CATEGORY}_${configId}", key)
            .firstResult()?.displayName
    }

    /**
     * Deactivate all default configurations
     */
    private fun deactivateAllDefaults() {
        val defaultConfigs = lookupValueRepository.find("category = ?1 AND sortOrder = 1 AND active = true",
                                                        UPID_CONFIG_CATEGORY).list()
        defaultConfigs.forEach { config ->
            config.sortOrder = 99
            config.updatedAt = Instant.now()
        }
    }

    /**
     * Create history entry
     */
    private fun createHistoryEntry(configId: UUID, action: String, oldValues: String?, newValues: String?,
                                  changedBy: String, changeReason: String) {
        val historyEntry = LookupValue(
            category = "UPID_CONFIG_HISTORY",
            code = "${configId}_${System.currentTimeMillis()}",
            displayName = "$action|$changedBy|$changeReason|${oldValues ?: ""}|${newValues ?: ""}",
            sortOrder = 0,
            active = true
        )
        lookupValueRepository.persist(historyEntry)
    }

    /**
     * Build configuration DTO from lookup values
     */
    private fun buildConfigurationDto(configId: UUID, configName: String): UpidConfigurationDto {
        val patternTemplate = getConfigurationComponent(configId, PATTERN_TEMPLATE_KEY) ?: "FACILITY-NETWORK-SEQUENCE"
        val networkIdDigits = getConfigurationComponent(configId, NETWORK_ID_DIGITS_KEY)?.toIntOrNull() ?: 2
        val networkIdValue = getConfigurationComponent(configId, NETWORK_ID_VALUE_KEY) ?: "00"
        val facilityIdDigits = getConfigurationComponent(configId, FACILITY_ID_DIGITS_KEY)?.toIntOrNull() ?: 3
        val facilityIdFormat = getConfigurationComponent(configId, FACILITY_ID_FORMAT_KEY) ?: "NUMERIC"
        val sequenceDigits = getConfigurationComponent(configId, SEQUENCE_DIGITS_KEY)?.toIntOrNull() ?: 8
        val sequenceFormat = getConfigurationComponent(configId, SEQUENCE_FORMAT_KEY) ?: "SPLIT"
        val separator = getConfigurationComponent(configId, SEPARATOR_KEY) ?: "-"
        val hospitalShortform = getConfigurationComponent(configId, HOSPITAL_SHORTFORM_KEY)
        val useHospitalShortform = getConfigurationComponent(configId, USE_HOSPITAL_SHORTFORM_KEY)?.toBoolean() ?: false

        val mainConfig = lookupValueRepository.findById(configId)

        return UpidConfigurationDto(
            configId = configId.toString().toLongOrNull(),
            configName = configName,
            patternRegex = generateRegexFromTemplate(patternTemplate, networkIdDigits, facilityIdDigits, sequenceDigits, facilityIdFormat, sequenceFormat, separator),
            patternTemplate = patternTemplate,
            description = mainConfig?.displayName,
            networkIdDigits = networkIdDigits,
            networkIdValue = networkIdValue,
            facilityIdDigits = facilityIdDigits,
            facilityIdFormat = FacilityIdFormat.valueOf(facilityIdFormat),
            sequenceDigits = sequenceDigits,
            sequenceFormat = SequenceFormat.valueOf(sequenceFormat),
            separator = separator,
            hospitalShortform = hospitalShortform,
            useHospitalShortform = useHospitalShortform,
            examplePattern = generateExamplePattern(patternTemplate, networkIdValue, facilityIdDigits, sequenceDigits, facilityIdFormat, sequenceFormat, separator, hospitalShortform),
            validationRegex = generateRegexFromTemplate(patternTemplate, networkIdDigits, facilityIdDigits, sequenceDigits, facilityIdFormat, sequenceFormat, separator),
            isActive = mainConfig?.active ?: true,
            isDefault = mainConfig?.sortOrder == 1,
            createdAt = mainConfig?.createdAt?.let { OffsetDateTime.ofInstant(it, java.time.ZoneOffset.UTC) },
            updatedAt = mainConfig?.updatedAt?.let { OffsetDateTime.ofInstant(it, java.time.ZoneOffset.UTC) }
        )
    }

    /**
     * Update existing UPID configuration using LookupValue table
     */
    @Transactional
    fun updateConfiguration(
        configId: Long,
        request: UpdateUpidConfigurationRequest,
        updatedBy: String = "ADMIN"
    ): UpidConfigurationDto {
        logger.info("Updating UPID configuration: $configId")

        val configUuid = UUID.fromString(configId.toString().padStart(36, '0'))
        val mainConfig = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        val oldValues = objectMapper.writeValueAsString(getCurrentConfigurationData(configUuid))

        // Update main configuration if needed
        request.configName?.let { newName ->
            // Check if new name already exists
            val existingConfig = lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true AND id != ?3",
                                                            UPID_CONFIG_CATEGORY, newName, configUuid).firstResult()
            if (existingConfig != null) {
                throw IllegalArgumentException("Configuration name '$newName' already exists")
            }
            mainConfig.code = newName
        }

        request.description?.let { mainConfig.displayName = it }

        request.isActive?.let { mainConfig.active = it }

        request.isDefault?.let { isDefault ->
            if (isDefault) {
                deactivateAllDefaults()
                mainConfig.sortOrder = 1
            } else {
                mainConfig.sortOrder = 99
            }
        }

        mainConfig.updatedAt = Instant.now()

        // Update components if provided
        request.patternTemplate?.let {
            updateConfigurationComponent(configUuid, PATTERN_TEMPLATE_KEY, it)
        }

        request.components?.let { components ->
            updateConfigurationComponent(configUuid, NETWORK_ID_DIGITS_KEY, components.networkId.digits.toString())
            components.networkId.value?.let {
                updateConfigurationComponent(configUuid, NETWORK_ID_VALUE_KEY, it)
            }
            updateConfigurationComponent(configUuid, FACILITY_ID_DIGITS_KEY, components.facilityId.digits.toString())
            updateConfigurationComponent(configUuid, FACILITY_ID_FORMAT_KEY, components.facilityId.format)
            updateConfigurationComponent(configUuid, SEQUENCE_DIGITS_KEY, components.sequence.digits.toString())
            updateConfigurationComponent(configUuid, SEQUENCE_FORMAT_KEY, components.sequence.format)
            updateConfigurationComponent(configUuid, SEPARATOR_KEY, components.separator)

            components.hospitalShortform?.let { shortform ->
                updateConfigurationComponent(configUuid, HOSPITAL_SHORTFORM_KEY, shortform.value ?: "")
                updateConfigurationComponent(configUuid, USE_HOSPITAL_SHORTFORM_KEY, shortform.enabled.toString())
            }
        }

        // Create history entry
        createHistoryEntry(configUuid, "UPDATE", oldValues, objectMapper.writeValueAsString(request),
                          updatedBy, request.changeReason ?: "Configuration updated")

        logger.info("UPID configuration updated successfully: $configId")
        return buildConfigurationDto(configUuid, mainConfig.code)
    }

    /**
     * Update configuration component
     */
    private fun updateConfigurationComponent(configId: UUID, key: String, value: String) {
        val existing = lookupValueRepository.find("category = ?1 AND code = ?2",
                                                 "${UPID_CONFIG_CATEGORY}_${configId}", key).firstResult()
        if (existing != null) {
            existing.displayName = value
            existing.updatedAt = Instant.now()
        } else {
            saveConfigurationComponent(configId, key, value)
        }
    }

    /**
     * Get current configuration data for history
     */
    private fun getCurrentConfigurationData(configId: UUID): Map<String, Any> {
        val mainConfig = lookupValueRepository.findById(configId)
        return mapOf(
            "configName" to (mainConfig?.code ?: ""),
            "description" to (mainConfig?.displayName ?: ""),
            "isActive" to (mainConfig?.active ?: true),
            "isDefault" to (mainConfig?.sortOrder == 1),
            "patternTemplate" to (getConfigurationComponent(configId, PATTERN_TEMPLATE_KEY) ?: ""),
            "networkIdDigits" to (getConfigurationComponent(configId, NETWORK_ID_DIGITS_KEY) ?: "2"),
            "networkIdValue" to (getConfigurationComponent(configId, NETWORK_ID_VALUE_KEY) ?: "00"),
            "facilityIdDigits" to (getConfigurationComponent(configId, FACILITY_ID_DIGITS_KEY) ?: "3"),
            "facilityIdFormat" to (getConfigurationComponent(configId, FACILITY_ID_FORMAT_KEY) ?: "NUMERIC"),
            "sequenceDigits" to (getConfigurationComponent(configId, SEQUENCE_DIGITS_KEY) ?: "8"),
            "sequenceFormat" to (getConfigurationComponent(configId, SEQUENCE_FORMAT_KEY) ?: "SPLIT"),
            "separator" to (getConfigurationComponent(configId, SEPARATOR_KEY) ?: "-"),
            "hospitalShortform" to (getConfigurationComponent(configId, HOSPITAL_SHORTFORM_KEY) ?: ""),
            "useHospitalShortform" to (getConfigurationComponent(configId, USE_HOSPITAL_SHORTFORM_KEY) ?: "false")
        )
    }

    /**
     * Get all UPID configurations
     */
    fun getAllConfigurations(): List<UpidConfigurationDto> {
        val configs = lookupValueRepository.find("category = ?1 AND active = true ORDER BY sortOrder, code",
                                                 UPID_CONFIG_CATEGORY).list()
        return configs.map { config ->
            buildConfigurationDto(config.id, config.code)
        }
    }

    /**
     * Get UPID configuration by ID
     */
    fun getConfigurationById(configId: Long): UpidConfigurationDto {
        val configUuid = UUID.fromString(configId.toString().padStart(36, '0'))
        val config = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")
        return buildConfigurationDto(configUuid, config.code)
    }

    /**
     * Get default UPID configuration
     */
    fun getDefaultConfiguration(): UpidConfigurationDto {
        val defaultConfig = lookupValueRepository.find("category = ?1 AND sortOrder = 1 AND active = true",
                                                       UPID_CONFIG_CATEGORY).firstResult()
            ?: throw IllegalArgumentException("No default configuration found")
        return buildConfigurationDto(defaultConfig.id, defaultConfig.code)
    }

    /**
     * Delete UPID configuration (soft delete)
     */
    @Transactional
    fun deleteConfiguration(configId: Long, deletedBy: String = "ADMIN"): Boolean {
        logger.info("Deleting UPID configuration: $configId")

        val configUuid = UUID.fromString(configId.toString().padStart(36, '0'))
        val config = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (config.sortOrder == 1) {
            throw IllegalArgumentException("Cannot delete default configuration")
        }

        val oldValues = objectMapper.writeValueAsString(getCurrentConfigurationData(configUuid))

        // Soft delete main config
        config.active = false
        config.updatedAt = Instant.now()

        // Soft delete all components
        val components = lookupValueRepository.find("category = ?1 AND active = true",
                                                   "${UPID_CONFIG_CATEGORY}_${configUuid}").list()
        components.forEach { component ->
            component.active = false
            component.updatedAt = Instant.now()
        }

        // Create history entry
        createHistoryEntry(configUuid, "DELETE", oldValues, null, deletedBy, "Configuration deleted")

        return true
    }

    /**
     * Set configuration as default
     */
    @Transactional
    fun setAsDefault(configId: Long, updatedBy: String = "ADMIN"): Boolean {
        logger.info("Setting UPID configuration as default: $configId")

        val configUuid = UUID.fromString(configId.toString().padStart(36, '0'))
        val config = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (!config.active) {
            throw IllegalArgumentException("Cannot set inactive configuration as default")
        }

        // Deactivate all defaults
        deactivateAllDefaults()

        // Set this as default
        config.sortOrder = 1
        config.updatedAt = Instant.now()

        // Create history entry
        createHistoryEntry(configUuid, "SET_DEFAULT", null, "Set as default configuration",
                          updatedBy, "Configuration set as default")

        return true
    }

    /**
     * Generate UPID using specified configuration
     */
    fun generateUpid(facilityId: String, configId: Long? = null): String {
        val config = if (configId != null) {
            val configUuid = UUID.fromString(configId.toString().padStart(36, '0'))
            lookupValueRepository.findById(configUuid)
                ?: throw IllegalArgumentException("Configuration not found: $configId")
        } else {
            lookupValueRepository.find("category = ?1 AND sortOrder = 1 AND active = true",
                                      UPID_CONFIG_CATEGORY).firstResult()
                ?: throw IllegalArgumentException("No default configuration found")
        }

        if (!config.active) {
            throw IllegalArgumentException("Configuration is not active: ${config.id}")
        }

        // Get next sequence number
        val sequence = getNextSequence(facilityId, config.id)

        // Generate UPID based on pattern
        val upid = generateUpidFromPattern(facilityId, sequence, config.id)

        logger.info("Generated UPID: $upid for facility: $facilityId using config: ${config.id}")
        return upid
    }

    /**
     * Get next sequence number for facility and config
     */
    private fun getNextSequence(facilityId: String, configId: UUID): Long {
        val counterKey = "${facilityId}_${configId}"
        val counter = lookupValueRepository.find("category = ?1 AND code = ?2",
                                                 UPID_SEQUENCE_COUNTER_CATEGORY, counterKey).firstResult()

        return if (counter != null) {
            val currentSequence = counter.displayName.toLongOrNull() ?: 0L
            val nextSequence = currentSequence + 1
            counter.displayName = nextSequence.toString()
            counter.updatedAt = Instant.now()
            nextSequence
        } else {
            // Create new counter starting from 1
            val newCounter = LookupValue(
                category = UPID_SEQUENCE_COUNTER_CATEGORY,
                code = counterKey,
                displayName = "1",
                sortOrder = 0,
                active = true
            )
            lookupValueRepository.persist(newCounter)
            1L
        }
    }

    /**
     * Generate UPID from pattern and configuration
     */
    private fun generateUpidFromPattern(facilityId: String, sequence: Long, configId: UUID): String {
        val patternTemplate = getConfigurationComponent(configId, PATTERN_TEMPLATE_KEY) ?: "FACILITY-NETWORK-SEQUENCE"
        val networkIdValue = getConfigurationComponent(configId, NETWORK_ID_VALUE_KEY) ?: "00"
        val facilityIdDigits = getConfigurationComponent(configId, FACILITY_ID_DIGITS_KEY)?.toIntOrNull() ?: 3
        val facilityIdFormat = getConfigurationComponent(configId, FACILITY_ID_FORMAT_KEY) ?: "NUMERIC"
        val sequenceDigits = getConfigurationComponent(configId, SEQUENCE_DIGITS_KEY)?.toIntOrNull() ?: 8
        val sequenceFormat = getConfigurationComponent(configId, SEQUENCE_FORMAT_KEY) ?: "SPLIT"
        val separator = getConfigurationComponent(configId, SEPARATOR_KEY) ?: "-"
        val hospitalShortform = getConfigurationComponent(configId, HOSPITAL_SHORTFORM_KEY)

        val parts = patternTemplate.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append(networkIdValue)
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (facilityIdFormat) {
                        "NUMERIC" -> facilityId.padStart(facilityIdDigits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(facilityIdDigits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(facilityIdDigits, '0')
                        else -> facilityId.padStart(facilityIdDigits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (sequenceFormat == "SPLIT" && sequenceDigits >= 4) {
                        val paddedSequence = sequence.toString().padStart(sequenceDigits, '0')
                        val firstPart = sequenceDigits / 2
                        val secondPart = sequenceDigits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(sequenceDigits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(hospitalShortform ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Validate UPID pattern
     */
    fun validatePattern(request: UpidPatternValidationRequest): UpidPatternValidationResponse {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        try {
            val patternInfo = generatePatternFromTemplate(request.patternTemplate, request.components)

            // Generate sample UPIDs
            val sampleUpids = mutableListOf<String>()
            repeat(request.sampleCount) { index ->
                val sequence = (index + 1).toLong()
                val sampleUpid = generateSampleUpid(request.testFacilityId, sequence, request.patternTemplate, request.components)
                sampleUpids.add(sampleUpid)
            }

            return UpidPatternValidationResponse(
                valid = errors.isEmpty(),
                errors = errors,
                warnings = warnings,
                generatedRegex = patternInfo.regex,
                validationRegex = patternInfo.validationRegex,
                sampleUpids = sampleUpids,
                patternBreakdown = patternInfo.breakdown
            )

        } catch (e: Exception) {
            errors.add("Pattern validation failed: ${e.message}")
            return UpidPatternValidationResponse(
                valid = false,
                errors = errors,
                warnings = warnings
            )
        }
    }

    /**
     * Generate sample UPID for validation
     */
    private fun generateSampleUpid(facilityId: String, sequence: Long, template: String, components: UpidComponentsDto): String {
        val separator = components.separator
        val parts = template.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append((components.networkId.value ?: "00").padStart(components.networkId.digits, '0'))
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (components.facilityId.format) {
                        "NUMERIC" -> facilityId.padStart(components.facilityId.digits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(components.facilityId.digits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(components.facilityId.digits, '0')
                        else -> facilityId.padStart(components.facilityId.digits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (components.sequence.format == "SPLIT" && components.sequence.digits >= 4) {
                        val paddedSequence = sequence.toString().padStart(components.sequence.digits, '0')
                        val firstPart = components.sequence.digits / 2
                        val secondPart = components.sequence.digits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(components.sequence.digits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(components.hospitalShortform?.value ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Get configuration history
     */
    fun getConfigurationHistory(configId: Long): List<Map<String, Any>> {
        val configUuid = UUID.fromString(configId.toString().padStart(36, '0'))
        val historyEntries = lookupValueRepository.find("category = ?1 AND code LIKE ?2 ORDER BY createdAt DESC",
                                                        "UPID_CONFIG_HISTORY", "${configUuid}_%").list()

        return historyEntries.map { history ->
            val parts = history.displayName.split("|")
            mapOf(
                "historyId" to history.id.toString(),
                "action" to (parts.getOrNull(0) ?: ""),
                "changedBy" to (parts.getOrNull(1) ?: ""),
                "changeReason" to (parts.getOrNull(2) ?: ""),
                "oldValues" to (parts.getOrNull(3) ?: ""),
                "newValues" to (parts.getOrNull(4) ?: ""),
                "createdAt" to OffsetDateTime.ofInstant(history.createdAt, java.time.ZoneOffset.UTC)
            )
        }
    }

    /**
     * Generate regex from template and parameters
     */
    private fun generateRegexFromTemplate(template: String, networkIdDigits: Int, facilityIdDigits: Int,
                                         sequenceDigits: Int, facilityIdFormat: String, sequenceFormat: String,
                                         separator: String): String {
        val regex = StringBuilder()
        val parts = template.split(separator)

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                regex.append("\\${separator}")
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    regex.append("\\d{$networkIdDigits}")
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    when (facilityIdFormat) {
                        "NUMERIC" -> regex.append("\\d{$facilityIdDigits}")
                        "ALPHA" -> regex.append("[A-Z]{$facilityIdDigits}")
                        "ALPHANUMERIC" -> regex.append("[A-Z0-9]{$facilityIdDigits}")
                        else -> regex.append("\\d{$facilityIdDigits}")
                    }
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (sequenceFormat == "SPLIT" && sequenceDigits >= 4) {
                        val firstPart = sequenceDigits / 2
                        val secondPart = sequenceDigits - firstPart
                        regex.append("\\d{$firstPart}\\${separator}\\d{$secondPart}")
                    } else {
                        regex.append("\\d{$sequenceDigits}")
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    regex.append("[A-Z]{3}")
                }
                else -> {
                    val escaped = Regex.escape(part)
                    regex.append(escaped)
                }
            }
        }

        return "^${regex}$"
    }

    /**
     * Generate example pattern
     */
    private fun generateExamplePattern(template: String, networkIdValue: String, facilityIdDigits: Int,
                                      sequenceDigits: Int, facilityIdFormat: String, sequenceFormat: String,
                                      separator: String, hospitalShortform: String?): String {
        val example = StringBuilder()
        val parts = template.split(separator)

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                example.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    example.append(networkIdValue)
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    when (facilityIdFormat) {
                        "NUMERIC" -> example.append("1".padStart(facilityIdDigits, '0'))
                        "ALPHA" -> example.append("A".repeat(facilityIdDigits))
                        "ALPHANUMERIC" -> example.append("A1".padEnd(facilityIdDigits, '0'))
                        else -> example.append("1".padStart(facilityIdDigits, '0'))
                    }
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (sequenceFormat == "SPLIT" && sequenceDigits >= 4) {
                        val firstPart = sequenceDigits / 2
                        val secondPart = sequenceDigits - firstPart
                        example.append("0".repeat(firstPart)).append(separator).append("1".padStart(secondPart, '0'))
                    } else {
                        example.append("1".padStart(sequenceDigits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    example.append(hospitalShortform ?: "HSP")
                }
                else -> {
                    example.append(part)
                }
            }
        }

        return example.toString()
    }

    /**
     * Generate pattern information from template and components
     */
    private fun generatePatternFromTemplate(template: String, components: UpidComponentsDto): PatternInfo {
        val regex = generateRegexFromTemplate(template, components.networkId.digits, components.facilityId.digits,
                                             components.sequence.digits, components.facilityId.format,
                                             components.sequence.format, components.separator)

        val example = generateExamplePattern(template, components.networkId.value ?: "00", components.facilityId.digits,
                                           components.sequence.digits, components.facilityId.format,
                                           components.sequence.format, components.separator,
                                           components.hospitalShortform?.value)

        val breakdown = mutableMapOf<String, String>()
        breakdown["network"] = "Network ID (${components.networkId.digits} digits)"
        breakdown["facility"] = "Facility ID (${components.facilityId.digits} digits, ${components.facilityId.format})"
        breakdown["sequence"] = "Sequence (${components.sequence.digits} digits, ${components.sequence.format} format)"
        if (components.hospitalShortform?.enabled == true) {
            breakdown["hospital"] = "Hospital shortform (${components.hospitalShortform.value?.length ?: 3} characters)"
        }

        return PatternInfo(
            regex = regex,
            validationRegex = regex,
            example = example,
            breakdown = breakdown
        )
    }

    /**
     * Data class for pattern information
     */
    private data class PatternInfo(
        val regex: String,
        val validationRegex: String,
        val example: String,
        val breakdown: Map<String, String>
    )
}

    /**
     * Get all UPID configurations
     */
    fun getAllConfigurations(): List<UpidConfigurationDto> {
        return upidConfigRepository.findAllActive().map { it.toDto() }
    }

    /**
     * Get UPID configuration by ID
     */
    fun getConfigurationById(configId: Long): UpidConfigurationDto {
        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")
        return entity.toDto()
    }

    /**
     * Get default UPID configuration
     */
    fun getDefaultConfiguration(): UpidConfigurationDto {
        val entity = upidConfigRepository.findDefaultActive()
            ?: throw IllegalArgumentException("No default configuration found")
        return entity.toDto()
    }

    /**
     * Delete UPID configuration (soft delete)
     */
    @Transactional
    fun deleteConfiguration(configId: Long, deletedBy: String = "ADMIN"): Boolean {
        logger.info("Deleting UPID configuration: $configId")

        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (entity.isDefault) {
            throw IllegalArgumentException("Cannot delete default configuration")
        }

        val oldValues = objectMapper.writeValueAsString(entity)
        val success = upidConfigRepository.softDelete(configId)

        if (success) {
            // Create history entry
            upidHistoryRepository.createHistoryEntry(
                configId = configId,
                action = "DELETE",
                oldValues = oldValues,
                newValues = null,
                changedBy = deletedBy,
                changeReason = "Configuration deleted"
            )
        }

        return success
    }

    /**
     * Set configuration as default
     */
    @Transactional
    fun setAsDefault(configId: Long, updatedBy: String = "ADMIN"): Boolean {
        logger.info("Setting UPID configuration as default: $configId")

        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (!entity.isActive) {
            throw IllegalArgumentException("Cannot set inactive configuration as default")
        }

        val success = upidConfigRepository.setAsDefault(configId)

        if (success) {
            // Create history entry
            upidHistoryRepository.createHistoryEntry(
                configId = configId,
                action = "SET_DEFAULT",
                oldValues = null,
                newValues = "Set as default configuration",
                changedBy = updatedBy,
                changeReason = "Configuration set as default"
            )
        }

        return success
    }

    /**
     * Generate UPID using specified configuration
     */
    fun generateUpid(facilityId: String, configId: Long? = null): String {
        val config = if (configId != null) {
            upidConfigRepository.findById(configId)
                ?: throw IllegalArgumentException("Configuration not found: $configId")
        } else {
            upidConfigRepository.findDefaultActive()
                ?: throw IllegalArgumentException("No default configuration found")
        }

        if (!config.isActive) {
            throw IllegalArgumentException("Configuration is not active: ${config.configId}")
        }

        // Get next sequence number
        val sequence = upidSequenceRepository.getNextSequence(facilityId, config.configId)

        // Generate UPID based on pattern
        val upid = generateUpidFromPattern(facilityId, sequence, config)

        // Update last generated UPID
        upidSequenceRepository.updateLastGeneratedUpid(facilityId, config.configId, upid)

        logger.info("Generated UPID: $upid for facility: $facilityId using config: ${config.configId}")
        return upid
    }

    /**
     * Validate UPID pattern
     */
    fun validatePattern(request: UpidPatternValidationRequest): UpidPatternValidationResponse {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        try {
            val patternInfo = generatePatternFromTemplate(request.patternTemplate, request.components)
            
            // Generate sample UPIDs
            val sampleUpids = mutableListOf<String>()
            repeat(request.sampleCount) { index ->
                val sequence = (index + 1).toLong()
                val sampleUpid = generateSampleUpid(request.testFacilityId, sequence, request.patternTemplate, request.components)
                sampleUpids.add(sampleUpid)
            }

            return UpidPatternValidationResponse(
                valid = errors.isEmpty(),
                errors = errors,
                warnings = warnings,
                generatedRegex = patternInfo.regex,
                validationRegex = patternInfo.validationRegex,
                sampleUpids = sampleUpids,
                patternBreakdown = patternInfo.breakdown
            )

        } catch (e: Exception) {
            errors.add("Pattern validation failed: ${e.message}")
            return UpidPatternValidationResponse(
                valid = false,
                errors = errors,
                warnings = warnings
            )
        }
    }

    /**
     * Get configuration history
     */
    fun getConfigurationHistory(configId: Long): List<Map<String, Any>> {
        return upidHistoryRepository.findByConfigId(configId).map { history ->
            mapOf(
                "historyId" to history.historyId,
                "action" to history.action,
                "changedBy" to history.changedBy,
                "changeReason" to (history.changeReason ?: ""),
                "createdAt" to history.createdAt,
                "oldValues" to (history.oldValues ?: ""),
                "newValues" to (history.newValues ?: "")
            )
        }
    }

    /**
     * Generate pattern information from template and components
     */
    private fun generatePatternFromTemplate(template: String, components: UpidComponentsDto): PatternInfo {
        val regex = StringBuilder()
        val validationRegex = StringBuilder()
        val example = StringBuilder()
        val breakdown = mutableMapOf<String, String>()

        // Parse template and generate corresponding regex
        val parts = template.split(components.separator)

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                regex.append("\\${components.separator}")
                validationRegex.append("\\${components.separator}")
                example.append(components.separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    val digits = components.networkId.digits
                    regex.append("\\d{$digits}")
                    validationRegex.append("\\d{$digits}")
                    example.append(components.networkId.value?.padStart(digits, '0') ?: "0".repeat(digits))
                    breakdown["network"] = "Network ID ($digits digits)"
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val digits = components.facilityId.digits
                    when (components.facilityId.format) {
                        "NUMERIC" -> {
                            regex.append("\\d{$digits}")
                            validationRegex.append("\\d{$digits}")
                            example.append("1".padStart(digits, '0'))
                        }
                        "ALPHA" -> {
                            regex.append("[A-Z]{$digits}")
                            validationRegex.append("[A-Z]{$digits}")
                            example.append("A".repeat(digits))
                        }
                        "ALPHANUMERIC" -> {
                            regex.append("[A-Z0-9]{$digits}")
                            validationRegex.append("[A-Z0-9]{$digits}")
                            example.append("A1".padEnd(digits, '0'))
                        }
                    }
                    breakdown["facility"] = "Facility ID ($digits digits, ${components.facilityId.format})"
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    val digits = components.sequence.digits
                    if (components.sequence.format == "SPLIT" && digits >= 4) {
                        val firstPart = digits / 2
                        val secondPart = digits - firstPart
                        regex.append("\\d{$firstPart}\\${components.separator}\\d{$secondPart}")
                        validationRegex.append("\\d{$firstPart}\\${components.separator}\\d{$secondPart}")
                        example.append("0".repeat(firstPart)).append(components.separator).append("1".padStart(secondPart, '0'))
                        breakdown["sequence"] = "Sequence ($digits digits, split format)"
                    } else {
                        regex.append("\\d{$digits}")
                        validationRegex.append("\\d{$digits}")
                        example.append("1".padStart(digits, '0'))
                        breakdown["sequence"] = "Sequence ($digits digits, continuous format)"
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    val shortform = components.hospitalShortform?.value ?: "HSP"
                    regex.append("[A-Z]{${shortform.length}}")
                    validationRegex.append("[A-Z]{${shortform.length}}")
                    example.append(shortform)
                    breakdown["hospital"] = "Hospital shortform (${shortform.length} characters)"
                }
                else -> {
                    // Literal part
                    val escaped = Regex.escape(part)
                    regex.append(escaped)
                    validationRegex.append(escaped)
                    example.append(part)
                    breakdown["literal_$index"] = "Literal: $part"
                }
            }
        }

        return PatternInfo(
            regex = "^${regex}$",
            validationRegex = "^${validationRegex}$",
            example = example.toString(),
            breakdown = breakdown
        )
    }

    /**
     * Generate UPID from pattern and configuration
     */
    private fun generateUpidFromPattern(facilityId: String, sequence: Long, config: UpidConfigurationEntity): String {
        val template = config.patternTemplate
        val separator = config.separator
        val parts = template.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append(config.networkIdValue.padStart(config.networkIdDigits, '0'))
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (config.facilityIdFormat) {
                        "NUMERIC" -> facilityId.padStart(config.facilityIdDigits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(config.facilityIdDigits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(config.facilityIdDigits, '0')
                        else -> facilityId.padStart(config.facilityIdDigits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (config.sequenceFormat == "SPLIT" && config.sequenceDigits >= 4) {
                        val paddedSequence = sequence.toString().padStart(config.sequenceDigits, '0')
                        val firstPart = config.sequenceDigits / 2
                        val secondPart = config.sequenceDigits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(config.sequenceDigits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(config.hospitalShortform ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Generate sample UPID for validation
     */
    private fun generateSampleUpid(facilityId: String, sequence: Long, template: String, components: UpidComponentsDto): String {
        val separator = components.separator
        val parts = template.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append((components.networkId.value ?: "00").padStart(components.networkId.digits, '0'))
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (components.facilityId.format) {
                        "NUMERIC" -> facilityId.padStart(components.facilityId.digits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(components.facilityId.digits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(components.facilityId.digits, '0')
                        else -> facilityId.padStart(components.facilityId.digits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (components.sequence.format == "SPLIT" && components.sequence.digits >= 4) {
                        val paddedSequence = sequence.toString().padStart(components.sequence.digits, '0')
                        val firstPart = components.sequence.digits / 2
                        val secondPart = components.sequence.digits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(components.sequence.digits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(components.hospitalShortform?.value ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Get current components from entity
     */
    private fun getCurrentComponents(entity: UpidConfigurationEntity): UpidComponentsDto {
        return UpidComponentsDto(
            networkId = ComponentConfigDto(
                digits = entity.networkIdDigits,
                format = "NUMERIC",
                value = entity.networkIdValue
            ),
            facilityId = ComponentConfigDto(
                digits = entity.facilityIdDigits,
                format = entity.facilityIdFormat
            ),
            sequence = ComponentConfigDto(
                digits = entity.sequenceDigits,
                format = entity.sequenceFormat
            ),
            separator = entity.separator,
            hospitalShortform = if (entity.useHospitalShortform) {
                HospitalShortformDto(
                    enabled = true,
                    value = entity.hospitalShortform
                )
            } else null
        )
    }

    /**
     * Data class for pattern information
     */
    private data class PatternInfo(
        val regex: String,
        val validationRegex: String,
        val example: String,
        val breakdown: Map<String, String>
    )
}

/**
 * Extension function to convert entity to DTO
 */
fun UpidConfigurationEntity.toDto(): UpidConfigurationDto {
    return UpidConfigurationDto(
        configId = this.configId,
        configName = this.configName,
        patternRegex = this.patternRegex,
        patternTemplate = this.patternTemplate,
        description = this.description,
        networkIdDigits = this.networkIdDigits,
        networkIdValue = this.networkIdValue,
        facilityIdDigits = this.facilityIdDigits,
        facilityIdFormat = FacilityIdFormat.valueOf(this.facilityIdFormat),
        sequenceDigits = this.sequenceDigits,
        sequenceFormat = SequenceFormat.valueOf(this.sequenceFormat),
        separator = this.separator,
        hospitalShortform = this.hospitalShortform,
        useHospitalShortform = this.useHospitalShortform,
        examplePattern = this.examplePattern,
        validationRegex = this.validationRegex,
        isActive = this.isActive,
        isDefault = this.isDefault,
        createdBy = this.createdBy,
        updatedBy = this.updatedBy,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        version = this.version
    )
}
