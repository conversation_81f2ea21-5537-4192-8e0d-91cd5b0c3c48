package sirobilt.meghasanjivini.patientregistration.service

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.UpidConfigurationEntity
import sirobilt.meghasanjivini.patientregistration.repository.UpidConfigurationRepository
import sirobilt.meghasanjivini.patientregistration.repository.UpidConfigurationHistoryRepository
import sirobilt.meghasanjivini.patientregistration.repository.UpidSequenceCounterRepository
import java.time.OffsetDateTime
import java.util.logging.Logger

/**
 * Advanced service for managing UPID configuration with regex patterns and configurable components
 */
@ApplicationScoped
class AdvancedUpidConfigurationService {

    @Inject
    lateinit var upidConfigRepository: UpidConfigurationRepository

    @Inject
    lateinit var upidHistoryRepository: UpidConfigurationHistoryRepository

    @Inject
    lateinit var upidSequenceRepository: UpidSequenceCounterRepository

    @Inject
    lateinit var objectMapper: ObjectMapper

    private val logger: Logger = Logger.getLogger(AdvancedUpidConfigurationService::class.java.name)

    /**
     * Create new UPID configuration
     */
    @Transactional
    fun createConfiguration(request: CreateUpidConfigurationRequest, createdBy: String = "ADMIN"): UpidConfigurationDto {
        logger.info("Creating new UPID configuration: ${request.configName}")

        // Check if configuration name already exists
        if (upidConfigRepository.existsByNameExcludingId(request.configName, null)) {
            throw IllegalArgumentException("Configuration name '${request.configName}' already exists")
        }

        // Generate regex pattern from template
        val patternInfo = generatePatternFromTemplate(request.patternTemplate, request.components)

        // Create entity
        val entity = UpidConfigurationEntity(
            configName = request.configName,
            patternRegex = patternInfo.regex,
            patternTemplate = request.patternTemplate,
            description = request.description,
            networkIdDigits = request.components.networkId.digits,
            networkIdValue = request.components.networkId.value ?: "00",
            facilityIdDigits = request.components.facilityId.digits,
            facilityIdFormat = request.components.facilityId.format,
            sequenceDigits = request.components.sequence.digits,
            sequenceFormat = request.components.sequence.format,
            separator = request.components.separator,
            hospitalShortform = request.components.hospitalShortform?.value,
            useHospitalShortform = request.components.hospitalShortform?.enabled ?: false,
            examplePattern = patternInfo.example,
            validationRegex = patternInfo.validationRegex,
            isDefault = request.isDefault,
            createdBy = createdBy,
            updatedBy = createdBy
        )

        // If this is set as default, deactivate other defaults
        if (request.isDefault) {
            upidConfigRepository.deactivateAllDefaults()
        }

        upidConfigRepository.persist(entity)

        // Create history entry
        upidHistoryRepository.createHistoryEntry(
            configId = entity.configId,
            action = "CREATE",
            oldValues = null,
            newValues = objectMapper.writeValueAsString(entity),
            changedBy = createdBy,
            changeReason = "New configuration created"
        )

        logger.info("UPID configuration created successfully: ${entity.configId}")
        return entity.toDto()
    }

    /**
     * Update existing UPID configuration
     */
    @Transactional
    fun updateConfiguration(
        configId: Long, 
        request: UpdateUpidConfigurationRequest, 
        updatedBy: String = "ADMIN"
    ): UpidConfigurationDto {
        logger.info("Updating UPID configuration: $configId")

        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        val oldValues = objectMapper.writeValueAsString(entity)

        // Update fields if provided
        request.configName?.let { 
            if (upidConfigRepository.existsByNameExcludingId(it, configId)) {
                throw IllegalArgumentException("Configuration name '$it' already exists")
            }
            entity.configName = it 
        }
        
        request.patternTemplate?.let { 
            val components = request.components ?: getCurrentComponents(entity)
            val patternInfo = generatePatternFromTemplate(it, components)
            entity.patternTemplate = it
            entity.patternRegex = patternInfo.regex
            entity.examplePattern = patternInfo.example
            entity.validationRegex = patternInfo.validationRegex
        }
        
        request.description?.let { entity.description = it }
        
        request.components?.let { components ->
            entity.networkIdDigits = components.networkId.digits
            entity.networkIdValue = components.networkId.value ?: entity.networkIdValue
            entity.facilityIdDigits = components.facilityId.digits
            entity.facilityIdFormat = components.facilityId.format
            entity.sequenceDigits = components.sequence.digits
            entity.sequenceFormat = components.sequence.format
            entity.separator = components.separator
            entity.hospitalShortform = components.hospitalShortform?.value
            entity.useHospitalShortform = components.hospitalShortform?.enabled ?: false
        }
        
        request.isActive?.let { entity.isActive = it }
        
        request.isDefault?.let { 
            if (it) {
                upidConfigRepository.deactivateAllDefaults()
            }
            entity.isDefault = it 
        }

        entity.updatedBy = updatedBy
        entity.updatedAt = OffsetDateTime.now()

        upidConfigRepository.persist(entity)

        // Create history entry
        upidHistoryRepository.createHistoryEntry(
            configId = entity.configId,
            action = "UPDATE",
            oldValues = oldValues,
            newValues = objectMapper.writeValueAsString(entity),
            changedBy = updatedBy,
            changeReason = request.changeReason ?: "Configuration updated"
        )

        logger.info("UPID configuration updated successfully: $configId")
        return entity.toDto()
    }

    /**
     * Get all UPID configurations
     */
    fun getAllConfigurations(): List<UpidConfigurationDto> {
        return upidConfigRepository.findAllActive().map { it.toDto() }
    }

    /**
     * Get UPID configuration by ID
     */
    fun getConfigurationById(configId: Long): UpidConfigurationDto {
        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")
        return entity.toDto()
    }

    /**
     * Get default UPID configuration
     */
    fun getDefaultConfiguration(): UpidConfigurationDto {
        val entity = upidConfigRepository.findDefaultActive()
            ?: throw IllegalArgumentException("No default configuration found")
        return entity.toDto()
    }

    /**
     * Delete UPID configuration (soft delete)
     */
    @Transactional
    fun deleteConfiguration(configId: Long, deletedBy: String = "ADMIN"): Boolean {
        logger.info("Deleting UPID configuration: $configId")

        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (entity.isDefault) {
            throw IllegalArgumentException("Cannot delete default configuration")
        }

        val oldValues = objectMapper.writeValueAsString(entity)
        val success = upidConfigRepository.softDelete(configId)

        if (success) {
            // Create history entry
            upidHistoryRepository.createHistoryEntry(
                configId = configId,
                action = "DELETE",
                oldValues = oldValues,
                newValues = null,
                changedBy = deletedBy,
                changeReason = "Configuration deleted"
            )
        }

        return success
    }

    /**
     * Set configuration as default
     */
    @Transactional
    fun setAsDefault(configId: Long, updatedBy: String = "ADMIN"): Boolean {
        logger.info("Setting UPID configuration as default: $configId")

        val entity = upidConfigRepository.findById(configId)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (!entity.isActive) {
            throw IllegalArgumentException("Cannot set inactive configuration as default")
        }

        val success = upidConfigRepository.setAsDefault(configId)

        if (success) {
            // Create history entry
            upidHistoryRepository.createHistoryEntry(
                configId = configId,
                action = "SET_DEFAULT",
                oldValues = null,
                newValues = "Set as default configuration",
                changedBy = updatedBy,
                changeReason = "Configuration set as default"
            )
        }

        return success
    }

    /**
     * Generate UPID using specified configuration
     */
    fun generateUpid(facilityId: String, configId: Long? = null): String {
        val config = if (configId != null) {
            upidConfigRepository.findById(configId)
                ?: throw IllegalArgumentException("Configuration not found: $configId")
        } else {
            upidConfigRepository.findDefaultActive()
                ?: throw IllegalArgumentException("No default configuration found")
        }

        if (!config.isActive) {
            throw IllegalArgumentException("Configuration is not active: ${config.configId}")
        }

        // Get next sequence number
        val sequence = upidSequenceRepository.getNextSequence(facilityId, config.configId)

        // Generate UPID based on pattern
        val upid = generateUpidFromPattern(facilityId, sequence, config)

        // Update last generated UPID
        upidSequenceRepository.updateLastGeneratedUpid(facilityId, config.configId, upid)

        logger.info("Generated UPID: $upid for facility: $facilityId using config: ${config.configId}")
        return upid
    }

    /**
     * Validate UPID pattern
     */
    fun validatePattern(request: UpidPatternValidationRequest): UpidPatternValidationResponse {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        try {
            val patternInfo = generatePatternFromTemplate(request.patternTemplate, request.components)
            
            // Generate sample UPIDs
            val sampleUpids = mutableListOf<String>()
            repeat(request.sampleCount) { index ->
                val sequence = (index + 1).toLong()
                val sampleUpid = generateSampleUpid(request.testFacilityId, sequence, request.patternTemplate, request.components)
                sampleUpids.add(sampleUpid)
            }

            return UpidPatternValidationResponse(
                valid = errors.isEmpty(),
                errors = errors,
                warnings = warnings,
                generatedRegex = patternInfo.regex,
                validationRegex = patternInfo.validationRegex,
                sampleUpids = sampleUpids,
                patternBreakdown = patternInfo.breakdown
            )

        } catch (e: Exception) {
            errors.add("Pattern validation failed: ${e.message}")
            return UpidPatternValidationResponse(
                valid = false,
                errors = errors,
                warnings = warnings
            )
        }
    }

    /**
     * Get configuration history
     */
    fun getConfigurationHistory(configId: Long): List<Map<String, Any>> {
        return upidHistoryRepository.findByConfigId(configId).map { history ->
            mapOf(
                "historyId" to history.historyId,
                "action" to history.action,
                "changedBy" to history.changedBy,
                "changeReason" to (history.changeReason ?: ""),
                "createdAt" to history.createdAt,
                "oldValues" to (history.oldValues ?: ""),
                "newValues" to (history.newValues ?: "")
            )
        }
    }

    /**
     * Generate pattern information from template and components
     */
    private fun generatePatternFromTemplate(template: String, components: UpidComponentsDto): PatternInfo {
        val regex = StringBuilder()
        val validationRegex = StringBuilder()
        val example = StringBuilder()
        val breakdown = mutableMapOf<String, String>()

        // Parse template and generate corresponding regex
        val parts = template.split(components.separator)

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                regex.append("\\${components.separator}")
                validationRegex.append("\\${components.separator}")
                example.append(components.separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    val digits = components.networkId.digits
                    regex.append("\\d{$digits}")
                    validationRegex.append("\\d{$digits}")
                    example.append(components.networkId.value?.padStart(digits, '0') ?: "0".repeat(digits))
                    breakdown["network"] = "Network ID ($digits digits)"
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val digits = components.facilityId.digits
                    when (components.facilityId.format) {
                        "NUMERIC" -> {
                            regex.append("\\d{$digits}")
                            validationRegex.append("\\d{$digits}")
                            example.append("1".padStart(digits, '0'))
                        }
                        "ALPHA" -> {
                            regex.append("[A-Z]{$digits}")
                            validationRegex.append("[A-Z]{$digits}")
                            example.append("A".repeat(digits))
                        }
                        "ALPHANUMERIC" -> {
                            regex.append("[A-Z0-9]{$digits}")
                            validationRegex.append("[A-Z0-9]{$digits}")
                            example.append("A1".padEnd(digits, '0'))
                        }
                    }
                    breakdown["facility"] = "Facility ID ($digits digits, ${components.facilityId.format})"
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    val digits = components.sequence.digits
                    if (components.sequence.format == "SPLIT" && digits >= 4) {
                        val firstPart = digits / 2
                        val secondPart = digits - firstPart
                        regex.append("\\d{$firstPart}\\${components.separator}\\d{$secondPart}")
                        validationRegex.append("\\d{$firstPart}\\${components.separator}\\d{$secondPart}")
                        example.append("0".repeat(firstPart)).append(components.separator).append("1".padStart(secondPart, '0'))
                        breakdown["sequence"] = "Sequence ($digits digits, split format)"
                    } else {
                        regex.append("\\d{$digits}")
                        validationRegex.append("\\d{$digits}")
                        example.append("1".padStart(digits, '0'))
                        breakdown["sequence"] = "Sequence ($digits digits, continuous format)"
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    val shortform = components.hospitalShortform?.value ?: "HSP"
                    regex.append("[A-Z]{${shortform.length}}")
                    validationRegex.append("[A-Z]{${shortform.length}}")
                    example.append(shortform)
                    breakdown["hospital"] = "Hospital shortform (${shortform.length} characters)"
                }
                else -> {
                    // Literal part
                    val escaped = Regex.escape(part)
                    regex.append(escaped)
                    validationRegex.append(escaped)
                    example.append(part)
                    breakdown["literal_$index"] = "Literal: $part"
                }
            }
        }

        return PatternInfo(
            regex = "^${regex}$",
            validationRegex = "^${validationRegex}$",
            example = example.toString(),
            breakdown = breakdown
        )
    }

    /**
     * Generate UPID from pattern and configuration
     */
    private fun generateUpidFromPattern(facilityId: String, sequence: Long, config: UpidConfigurationEntity): String {
        val template = config.patternTemplate
        val separator = config.separator
        val parts = template.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append(config.networkIdValue.padStart(config.networkIdDigits, '0'))
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (config.facilityIdFormat) {
                        "NUMERIC" -> facilityId.padStart(config.facilityIdDigits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(config.facilityIdDigits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(config.facilityIdDigits, '0')
                        else -> facilityId.padStart(config.facilityIdDigits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (config.sequenceFormat == "SPLIT" && config.sequenceDigits >= 4) {
                        val paddedSequence = sequence.toString().padStart(config.sequenceDigits, '0')
                        val firstPart = config.sequenceDigits / 2
                        val secondPart = config.sequenceDigits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(config.sequenceDigits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(config.hospitalShortform ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Generate sample UPID for validation
     */
    private fun generateSampleUpid(facilityId: String, sequence: Long, template: String, components: UpidComponentsDto): String {
        val separator = components.separator
        val parts = template.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append((components.networkId.value ?: "00").padStart(components.networkId.digits, '0'))
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (components.facilityId.format) {
                        "NUMERIC" -> facilityId.padStart(components.facilityId.digits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(components.facilityId.digits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(components.facilityId.digits, '0')
                        else -> facilityId.padStart(components.facilityId.digits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (components.sequence.format == "SPLIT" && components.sequence.digits >= 4) {
                        val paddedSequence = sequence.toString().padStart(components.sequence.digits, '0')
                        val firstPart = components.sequence.digits / 2
                        val secondPart = components.sequence.digits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(components.sequence.digits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(components.hospitalShortform?.value ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Get current components from entity
     */
    private fun getCurrentComponents(entity: UpidConfigurationEntity): UpidComponentsDto {
        return UpidComponentsDto(
            networkId = ComponentConfigDto(
                digits = entity.networkIdDigits,
                format = "NUMERIC",
                value = entity.networkIdValue
            ),
            facilityId = ComponentConfigDto(
                digits = entity.facilityIdDigits,
                format = entity.facilityIdFormat
            ),
            sequence = ComponentConfigDto(
                digits = entity.sequenceDigits,
                format = entity.sequenceFormat
            ),
            separator = entity.separator,
            hospitalShortform = if (entity.useHospitalShortform) {
                HospitalShortformDto(
                    enabled = true,
                    value = entity.hospitalShortform
                )
            } else null
        )
    }

    /**
     * Data class for pattern information
     */
    private data class PatternInfo(
        val regex: String,
        val validationRegex: String,
        val example: String,
        val breakdown: Map<String, String>
    )
}

/**
 * Extension function to convert entity to DTO
 */
fun UpidConfigurationEntity.toDto(): UpidConfigurationDto {
    return UpidConfigurationDto(
        configId = this.configId,
        configName = this.configName,
        patternRegex = this.patternRegex,
        patternTemplate = this.patternTemplate,
        description = this.description,
        networkIdDigits = this.networkIdDigits,
        networkIdValue = this.networkIdValue,
        facilityIdDigits = this.facilityIdDigits,
        facilityIdFormat = FacilityIdFormat.valueOf(this.facilityIdFormat),
        sequenceDigits = this.sequenceDigits,
        sequenceFormat = SequenceFormat.valueOf(this.sequenceFormat),
        separator = this.separator,
        hospitalShortform = this.hospitalShortform,
        useHospitalShortform = this.useHospitalShortform,
        examplePattern = this.examplePattern,
        validationRegex = this.validationRegex,
        isActive = this.isActive,
        isDefault = this.isDefault,
        createdBy = this.createdBy,
        updatedBy = this.updatedBy,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        version = this.version
    )
}
