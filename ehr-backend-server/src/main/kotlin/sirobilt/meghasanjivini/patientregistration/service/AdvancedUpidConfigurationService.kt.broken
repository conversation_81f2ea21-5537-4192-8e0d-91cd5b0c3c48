package sirobilt.meghasanjivini.patientregistration.service

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.masterdata.repository.LookupValueRepository
import sirobilt.meghasanjivini.masterdata.model.LookupValue
import sirobilt.meghasanjivini.masterdata.service.LookupService
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.OffsetDateTime
import java.time.Instant
import java.util.logging.Logger
import java.util.UUID

/**
 * Advanced service for managing UPID configuration using LookupValue table
 * Supports regex patterns and configurable components stored as lookup values
 */
@ApplicationScoped
class AdvancedUpidConfigurationService {

    @Inject
    lateinit var lookupValueRepository: LookupValueRepository

    @Inject
    lateinit var lookupService: LookupService

    @Inject
    lateinit var patientRepository: PatientRepository

    @Inject
    lateinit var objectMapper: ObjectMapper

    private val logger: Logger = Logger.getLogger(AdvancedUpidConfigurationService::class.java.name)

    // UPID Configuration Categories in LookupValue table
    companion object {
        const val UPID_CONFIG_CATEGORY = "UPID_CONFIG"
        const val UPID_NETWORK_ID_CATEGORY = "UPID_NETWORK_ID"
        const val UPID_FACILITY_FORMAT_CATEGORY = "UPID_FACILITY_FORMAT"
        const val UPID_SEQUENCE_FORMAT_CATEGORY = "UPID_SEQUENCE_FORMAT"
        const val UPID_HOSPITAL_SHORTFORM_CATEGORY = "UPID_HOSPITAL_SHORTFORM"
        const val UPID_SEQUENCE_COUNTER_CATEGORY = "UPID_SEQUENCE_COUNTER"
        
        // Configuration keys
        const val DEFAULT_CONFIG_KEY = "DEFAULT_CONFIG"
        const val PATTERN_TEMPLATE_KEY = "PATTERN_TEMPLATE"
        const val NETWORK_ID_DIGITS_KEY = "NETWORK_ID_DIGITS"
        const val NETWORK_ID_VALUE_KEY = "NETWORK_ID_VALUE"
        const val FACILITY_ID_DIGITS_KEY = "FACILITY_ID_DIGITS"
        const val FACILITY_ID_FORMAT_KEY = "FACILITY_ID_FORMAT"
        const val SEQUENCE_DIGITS_KEY = "SEQUENCE_DIGITS"
        const val SEQUENCE_FORMAT_KEY = "SEQUENCE_FORMAT"
        const val SEPARATOR_KEY = "SEPARATOR"
        const val HOSPITAL_SHORTFORM_KEY = "HOSPITAL_SHORTFORM"
        const val USE_HOSPITAL_SHORTFORM_KEY = "USE_HOSPITAL_SHORTFORM"
    }

    /**
     * Create new UPID configuration using LookupValue table with flattened structure
     */
    @Transactional
    fun createConfiguration(request: CreateUpidConfigurationRequest, createdBy: String = "ADMIN"): UpidConfigurationDto {
        logger.info("Creating new UPID configuration: ${request.configName}")

        // Check if configuration name already exists
        val existingConfig = lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true",
                                                        UPID_CONFIG_CATEGORY, request.configName).firstResult()
        if (existingConfig != null) {
            throw IllegalArgumentException("Configuration name '${request.configName}' already exists")
        }

        // If this is set as default, deactivate other defaults
        if (request.isDefault) {
            deactivateAllDefaults()
        }

        // Create a unique config identifier
        val configCode = request.configName.replace(" ", "_").uppercase()

        // Store each configuration component as separate LookupValue entries
        val configEntries = mutableListOf<LookupValue>()

        // Main configuration entry
        configEntries.add(LookupValue(
            category = UPID_CONFIG_CATEGORY,
            code = configCode,
            displayName = request.description ?: request.configName,
            sortOrder = if (request.isDefault) 1 else 99,
            active = true
        ))

        // Pattern template
        configEntries.add(LookupValue(
            category = "${UPID_CONFIG_CATEGORY}_PATTERN",
            code = configCode,
            displayName = request.patternTemplate,
            sortOrder = 0,
            active = true
        ))

        // Network ID configuration
        configEntries.add(LookupValue(
            category = "${UPID_CONFIG_CATEGORY}_NETWORK",
            code = configCode,
            displayName = "${request.components.networkId.digits}|${request.components.networkId.format}|${request.components.networkId.value ?: "00"}",
            sortOrder = 0,
            active = true
        ))

        // Facility ID configuration
        configEntries.add(LookupValue(
            category = "${UPID_CONFIG_CATEGORY}_FACILITY",
            code = configCode,
            displayName = "${request.components.facilityId.digits}|${request.components.facilityId.format}",
            sortOrder = 0,
            active = true
        ))

        // Sequence configuration
        configEntries.add(LookupValue(
            category = "${UPID_CONFIG_CATEGORY}_SEQUENCE",
            code = configCode,
            displayName = "${request.components.sequence.digits}|${request.components.sequence.format}",
            sortOrder = 0,
            active = true
        ))

        // Separator configuration
        configEntries.add(LookupValue(
            category = "${UPID_CONFIG_CATEGORY}_SEPARATOR",
            code = configCode,
            displayName = request.components.separator,
            sortOrder = 0,
            active = true
        ))

        // Hospital shortform configuration (if enabled)
        request.components.hospitalShortform?.let { shortform ->
            configEntries.add(LookupValue(
                category = "${UPID_CONFIG_CATEGORY}_HOSPITAL",
                code = configCode,
                displayName = "${shortform.enabled}|${shortform.value ?: ""}",
                sortOrder = 0,
                active = true
            ))
        }

        // Persist all entries
        configEntries.forEach { entry ->
            lookupValueRepository.persist(entry)
        }

        // Create history entry
        createHistoryEntry(configCode, "CREATE", null, objectMapper.writeValueAsString(request), createdBy, "New configuration created")

        logger.info("UPID configuration created successfully: $configCode")
        return buildConfigurationDto(configCode, request.configName)
    }

    /**
     * Get configuration component value by config code and category
     */
    private fun getConfigurationComponent(configCode: String, componentCategory: String): String? {
        return lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true",
                                         componentCategory, configCode)
            .firstResult()?.displayName
    }

    /**
     * Parse network configuration from display_name
     */
    private fun parseNetworkConfig(displayName: String): Triple<Int, String, String> {
        val parts = displayName.split("|")
        return Triple(
            parts.getOrNull(0)?.toIntOrNull() ?: 2,
            parts.getOrNull(1) ?: "NUMERIC",
            parts.getOrNull(2) ?: "00"
        )
    }

    /**
     * Parse facility configuration from display_name
     */
    private fun parseFacilityConfig(displayName: String): Pair<Int, String> {
        val parts = displayName.split("|")
        return Pair(
            parts.getOrNull(0)?.toIntOrNull() ?: 3,
            parts.getOrNull(1) ?: "NUMERIC"
        )
    }

    /**
     * Parse sequence configuration from display_name
     */
    private fun parseSequenceConfig(displayName: String): Pair<Int, String> {
        val parts = displayName.split("|")
        return Pair(
            parts.getOrNull(0)?.toIntOrNull() ?: 8,
            parts.getOrNull(1) ?: "SPLIT"
        )
    }

    /**
     * Parse hospital configuration from display_name
     */
    private fun parseHospitalConfig(displayName: String): Pair<Boolean, String> {
        val parts = displayName.split("|")
        return Pair(
            parts.getOrNull(0)?.toBoolean() ?: false,
            parts.getOrNull(1) ?: ""
        )
    }

    /**
     * Deactivate all default configurations
     */
    private fun deactivateAllDefaults() {
        val defaultConfigs = lookupValueRepository.find("category = ?1 AND sortOrder = 1 AND active = true", 
                                                        UPID_CONFIG_CATEGORY).list()
        defaultConfigs.forEach { config ->
            config.sortOrder = 99
            config.updatedAt = Instant.now()
        }
    }

    /**
     * Create history entry
     */
    private fun createHistoryEntry(configCode: String, action: String, oldValues: String?, newValues: String?,
                                  changedBy: String, changeReason: String) {
        // Truncate the history entry to fit in display_name column (255 chars)
        val historyData = "$action|$changedBy|$changeReason"
        val truncatedHistory = if (historyData.length > 250) {
            historyData.substring(0, 250) + "..."
        } else {
            historyData
        }

        val historyEntry = LookupValue(
            category = "UPID_CONFIG_HISTORY",
            code = "${configCode}_${System.currentTimeMillis()}",
            displayName = truncatedHistory,
            sortOrder = 0,
            active = true
        )
        lookupValueRepository.persist(historyEntry)
    }

    /**
     * Build configuration DTO from lookup values using flattened structure
     */
    private fun buildConfigurationDto(configCode: String, configName: String): UpidConfigurationDto {
        // Get pattern template
        val patternTemplate = getConfigurationComponent(configCode, "${UPID_CONFIG_CATEGORY}_PATTERN") ?: "FACILITY-NETWORK-SEQUENCE"

        // Get network configuration
        val networkConfig = getConfigurationComponent(configCode, "${UPID_CONFIG_CATEGORY}_NETWORK")
        val (networkIdDigits, networkIdFormat, networkIdValue) = if (networkConfig != null) {
            parseNetworkConfig(networkConfig)
        } else {
            Triple(2, "NUMERIC", "00")
        }

        // Get facility configuration
        val facilityConfig = getConfigurationComponent(configCode, "${UPID_CONFIG_CATEGORY}_FACILITY")
        val (facilityIdDigits, facilityIdFormat) = if (facilityConfig != null) {
            parseFacilityConfig(facilityConfig)
        } else {
            Pair(3, "NUMERIC")
        }

        // Get sequence configuration
        val sequenceConfig = getConfigurationComponent(configCode, "${UPID_CONFIG_CATEGORY}_SEQUENCE")
        val (sequenceDigits, sequenceFormat) = if (sequenceConfig != null) {
            parseSequenceConfig(sequenceConfig)
        } else {
            Pair(8, "SPLIT")
        }

        // Get separator
        val separator = getConfigurationComponent(configCode, "${UPID_CONFIG_CATEGORY}_SEPARATOR") ?: "-"

        // Get hospital configuration
        val hospitalConfig = getConfigurationComponent(configCode, "${UPID_CONFIG_CATEGORY}_HOSPITAL")
        val (useHospitalShortform, hospitalShortform) = if (hospitalConfig != null) {
            parseHospitalConfig(hospitalConfig)
        } else {
            Pair(false, "")
        }

        // Get main config for metadata
        val mainConfig = lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true",
                                                   UPID_CONFIG_CATEGORY, configCode).firstResult()

        // Generate a simple Long ID based on the position in the list
        val allConfigs = lookupValueRepository.find("category = ?1 AND active = true ORDER BY sortOrder, code",
                                                    UPID_CONFIG_CATEGORY).list()
        val configIndex = allConfigs.indexOfFirst { it.code == configCode } + 1

        return UpidConfigurationDto(
            configId = configIndex.toLong(),
            configName = configName,
            patternRegex = generateRegexFromTemplate(patternTemplate, networkIdDigits, facilityIdDigits, sequenceDigits, facilityIdFormat, sequenceFormat, separator),
            patternTemplate = patternTemplate,
            description = mainConfig?.displayName,
            networkIdDigits = networkIdDigits,
            networkIdValue = networkIdValue,
            facilityIdDigits = facilityIdDigits,
            facilityIdFormat = FacilityIdFormat.valueOf(facilityIdFormat),
            sequenceDigits = sequenceDigits,
            sequenceFormat = SequenceFormat.valueOf(sequenceFormat),
            separator = separator,
            hospitalShortform = if (hospitalShortform.isNotEmpty()) hospitalShortform else null,
            useHospitalShortform = useHospitalShortform,
            examplePattern = generateExamplePattern(patternTemplate, networkIdValue, facilityIdDigits, sequenceDigits, facilityIdFormat, sequenceFormat, separator, hospitalShortform),
            validationRegex = generateRegexFromTemplate(patternTemplate, networkIdDigits, facilityIdDigits, sequenceDigits, facilityIdFormat, sequenceFormat, separator),
            isActive = mainConfig?.active ?: true,
            isDefault = mainConfig?.sortOrder == 1,
            createdAt = mainConfig?.createdAt?.let { OffsetDateTime.ofInstant(it, java.time.ZoneOffset.UTC) },
            updatedAt = mainConfig?.updatedAt?.let { OffsetDateTime.ofInstant(it, java.time.ZoneOffset.UTC) }
        )
    }

    /**
     * Find configuration UUID by ID (handles conversion from Long to UUID)
     */
    private fun findConfigUuidById(configId: Long): UUID {
        // For simplicity, we'll search by the config name or use a mapping
        // In a real implementation, you might want to store the mapping differently
        val configs = lookupValueRepository.find("category = ?1 AND active = true ORDER BY sortOrder, code", 
                                                 UPID_CONFIG_CATEGORY).list()
        
        return if (configId <= configs.size) {
            configs[configId.toInt() - 1].id
        } else {
            throw IllegalArgumentException("Configuration not found: $configId")
        }
    }

    /**
     * Update configuration component
     */
    private fun updateConfigurationComponent(configId: UUID, key: String, value: String) {
        val existing = lookupValueRepository.find("category = ?1 AND code = ?2", 
                                                 "${UPID_CONFIG_CATEGORY}_${configId}", key).firstResult()
        if (existing != null) {
            existing.displayName = value
            existing.updatedAt = Instant.now()
        } else {
            saveConfigurationComponent(configId, key, value)
        }
    }

    /**
     * Get current configuration data for history
     */
    private fun getCurrentConfigurationData(configId: UUID): Map<String, Any> {
        val mainConfig = lookupValueRepository.findById(configId)
        return mapOf(
            "configName" to (mainConfig?.code ?: ""),
            "description" to (mainConfig?.displayName ?: ""),
            "isActive" to (mainConfig?.active ?: true),
            "isDefault" to (mainConfig?.sortOrder == 1),
            "patternTemplate" to (getConfigurationComponent(configId, PATTERN_TEMPLATE_KEY) ?: ""),
            "networkIdDigits" to (getConfigurationComponent(configId, NETWORK_ID_DIGITS_KEY) ?: "2"),
            "networkIdValue" to (getConfigurationComponent(configId, NETWORK_ID_VALUE_KEY) ?: "00"),
            "facilityIdDigits" to (getConfigurationComponent(configId, FACILITY_ID_DIGITS_KEY) ?: "3"),
            "facilityIdFormat" to (getConfigurationComponent(configId, FACILITY_ID_FORMAT_KEY) ?: "NUMERIC"),
            "sequenceDigits" to (getConfigurationComponent(configId, SEQUENCE_DIGITS_KEY) ?: "8"),
            "sequenceFormat" to (getConfigurationComponent(configId, SEQUENCE_FORMAT_KEY) ?: "SPLIT"),
            "separator" to (getConfigurationComponent(configId, SEPARATOR_KEY) ?: "-"),
            "hospitalShortform" to (getConfigurationComponent(configId, HOSPITAL_SHORTFORM_KEY) ?: ""),
            "useHospitalShortform" to (getConfigurationComponent(configId, USE_HOSPITAL_SHORTFORM_KEY) ?: "false")
        )
    }

    /**
     * Get all UPID configurations
     */
    fun getAllConfigurations(): List<UpidConfigurationDto> {
        val configs = lookupValueRepository.find("category = ?1 AND active = true ORDER BY sortOrder, code",
                                                 UPID_CONFIG_CATEGORY).list()
        return configs.map { config ->
            buildConfigurationDto(config.id, config.code)
        }
    }

    /**
     * Get UPID configuration by ID
     */
    fun getConfigurationById(configId: Long): UpidConfigurationDto {
        val configUuid = findConfigUuidById(configId)
        val config = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")
        return buildConfigurationDto(configUuid, config.code)
    }

    /**
     * Get default UPID configuration
     */
    fun getDefaultConfiguration(): UpidConfigurationDto {
        val defaultConfig = lookupValueRepository.find("category = ?1 AND sortOrder = 1 AND active = true",
                                                       UPID_CONFIG_CATEGORY).firstResult()
            ?: throw IllegalArgumentException("No default configuration found")
        return buildConfigurationDto(defaultConfig.id, defaultConfig.code)
    }

    /**
     * Generate UPID using specified configuration
     */
    @Transactional
    fun generateUpid(facilityId: String, configId: Long? = null): String {
        val config = if (configId != null) {
            val configUuid = findConfigUuidById(configId)
            lookupValueRepository.findById(configUuid)
                ?: throw IllegalArgumentException("Configuration not found: $configId")
        } else {
            lookupValueRepository.find("category = ?1 AND sortOrder = 1 AND active = true",
                                      UPID_CONFIG_CATEGORY).firstResult()
                ?: throw IllegalArgumentException("No default configuration found")
        }

        if (!config.active) {
            throw IllegalArgumentException("Configuration is not active: ${config.id}")
        }

        // Get next sequence number
        val sequence = getNextSequence(facilityId, config.id)

        // Generate UPID based on pattern
        val upid = generateUpidFromPattern(facilityId, sequence, config.id)

        logger.info("Generated UPID: $upid for facility: $facilityId using config: ${config.id}")
        return upid
    }

    /**
     * Get next sequence number for facility and config
     */
    private fun getNextSequence(facilityId: String, configId: UUID): Long {
        val counterKey = "${facilityId}_${configId}"
        val counter = lookupValueRepository.find("category = ?1 AND code = ?2",
                                                 UPID_SEQUENCE_COUNTER_CATEGORY, counterKey).firstResult()

        return if (counter != null) {
            val currentSequence = counter.displayName.toLongOrNull() ?: 0L
            val nextSequence = currentSequence + 1
            counter.displayName = nextSequence.toString()
            counter.updatedAt = Instant.now()
            nextSequence
        } else {
            // Create new counter starting from 1
            val newCounter = LookupValue(
                category = UPID_SEQUENCE_COUNTER_CATEGORY,
                code = counterKey,
                displayName = "1",
                sortOrder = 0,
                active = true
            )
            lookupValueRepository.persist(newCounter)
            1L
        }
    }

    /**
     * Generate UPID from pattern and configuration
     */
    private fun generateUpidFromPattern(facilityId: String, sequence: Long, configId: UUID): String {
        val patternTemplate = getConfigurationComponent(configId, PATTERN_TEMPLATE_KEY) ?: "FACILITY-NETWORK-SEQUENCE"
        val networkIdValue = getConfigurationComponent(configId, NETWORK_ID_VALUE_KEY) ?: "00"
        val facilityIdDigits = getConfigurationComponent(configId, FACILITY_ID_DIGITS_KEY)?.toIntOrNull() ?: 3
        val facilityIdFormat = getConfigurationComponent(configId, FACILITY_ID_FORMAT_KEY) ?: "NUMERIC"
        val sequenceDigits = getConfigurationComponent(configId, SEQUENCE_DIGITS_KEY)?.toIntOrNull() ?: 8
        val sequenceFormat = getConfigurationComponent(configId, SEQUENCE_FORMAT_KEY) ?: "SPLIT"
        val separator = getConfigurationComponent(configId, SEPARATOR_KEY) ?: "-"
        val hospitalShortform = getConfigurationComponent(configId, HOSPITAL_SHORTFORM_KEY)

        val result = StringBuilder()

        // Handle compact format (no separator) by processing the template directly
        if (separator.isEmpty()) {
            var processedTemplate = patternTemplate

            // Replace FACILITY
            if (processedTemplate.contains("FACILITY")) {
                val paddedFacilityId = when (facilityIdFormat) {
                    "NUMERIC" -> facilityId.padStart(facilityIdDigits, '0')
                    "ALPHA" -> facilityId.uppercase().padStart(facilityIdDigits, 'A')
                    "ALPHANUMERIC" -> facilityId.uppercase().padStart(facilityIdDigits, '0')
                    else -> facilityId.padStart(facilityIdDigits, '0')
                }
                processedTemplate = processedTemplate.replace("FACILITY", paddedFacilityId)
            }

            // Replace NETWORK
            if (processedTemplate.contains("NETWORK")) {
                processedTemplate = processedTemplate.replace("NETWORK", networkIdValue)
            }

            // Replace SEQUENCE
            if (processedTemplate.contains("SEQUENCE")) {
                val paddedSequence = sequence.toString().padStart(sequenceDigits, '0')
                processedTemplate = processedTemplate.replace("SEQUENCE", paddedSequence)
            }

            // Replace HOSPITAL
            if (processedTemplate.contains("HOSPITAL")) {
                processedTemplate = processedTemplate.replace("HOSPITAL", hospitalShortform ?: "HSP")
            }

            result.append(processedTemplate)
        } else {
            // Handle format with separators
            val parts = patternTemplate.split(separator)

            parts.forEachIndexed { index, part ->
                if (index > 0) {
                    result.append(separator)
                }

                when {
                    part.contains("NETWORK") || part.contains("NN") -> {
                        result.append(networkIdValue)
                    }
                    part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                        val paddedFacilityId = when (facilityIdFormat) {
                            "NUMERIC" -> facilityId.padStart(facilityIdDigits, '0')
                            "ALPHA" -> facilityId.uppercase().padStart(facilityIdDigits, 'A')
                            "ALPHANUMERIC" -> facilityId.uppercase().padStart(facilityIdDigits, '0')
                            else -> facilityId.padStart(facilityIdDigits, '0')
                        }
                        result.append(paddedFacilityId)
                    }
                    part.contains("SEQUENCE") || part.contains("NNNN") -> {
                        if (sequenceFormat == "SPLIT" && sequenceDigits >= 4) {
                            val paddedSequence = sequence.toString().padStart(sequenceDigits, '0')
                            val firstPart = sequenceDigits / 2
                            val secondPart = sequenceDigits - firstPart
                            result.append(paddedSequence.substring(0, firstPart))
                            result.append(separator)
                            result.append(paddedSequence.substring(firstPart))
                        } else {
                            result.append(sequence.toString().padStart(sequenceDigits, '0'))
                        }
                    }
                    part.contains("HOSPITAL") || part.contains("HHH") -> {
                        result.append(hospitalShortform ?: "HSP")
                    }
                    else -> {
                        result.append(part)
                    }
                }
            }
        }

        return result.toString()
    }

    /**
     * Validate UPID pattern
     */
    fun validatePattern(request: UpidPatternValidationRequest): UpidPatternValidationResponse {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        try {
            val patternInfo = generatePatternFromTemplate(request.patternTemplate, request.components)

            // Generate sample UPIDs
            val sampleUpids = mutableListOf<String>()
            repeat(request.sampleCount) { index ->
                val sequence = (index + 1).toLong()
                val sampleUpid = generateSampleUpid(request.testFacilityId, sequence, request.patternTemplate, request.components)
                sampleUpids.add(sampleUpid)
            }

            return UpidPatternValidationResponse(
                valid = errors.isEmpty(),
                errors = errors,
                warnings = warnings,
                generatedRegex = patternInfo.regex,
                validationRegex = patternInfo.validationRegex,
                sampleUpids = sampleUpids,
                patternBreakdown = patternInfo.breakdown
            )

        } catch (e: Exception) {
            errors.add("Pattern validation failed: ${e.message}")
            return UpidPatternValidationResponse(
                valid = false,
                errors = errors,
                warnings = warnings
            )
        }
    }

    /**
     * Update existing UPID configuration using LookupValue table
     */
    @Transactional
    fun updateConfiguration(
        configId: Long,
        request: UpdateUpidConfigurationRequest,
        updatedBy: String = "ADMIN"
    ): UpidConfigurationDto {
        logger.info("Updating UPID configuration: $configId")

        val configUuid = findConfigUuidById(configId)
        val mainConfig = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        val oldValues = objectMapper.writeValueAsString(getCurrentConfigurationData(configUuid))

        // Update main configuration if needed
        request.configName?.let { newName ->
            // Check if new name already exists
            val existingConfig = lookupValueRepository.find("category = ?1 AND code = ?2 AND active = true AND id != ?3",
                                                            UPID_CONFIG_CATEGORY, newName, configUuid).firstResult()
            if (existingConfig != null) {
                throw IllegalArgumentException("Configuration name '$newName' already exists")
            }
            mainConfig.code = newName
        }

        request.description?.let { mainConfig.displayName = it }

        request.isActive?.let { mainConfig.active = it }

        request.isDefault?.let { isDefault ->
            if (isDefault) {
                deactivateAllDefaults()
                mainConfig.sortOrder = 1
            } else {
                mainConfig.sortOrder = 99
            }
        }

        mainConfig.updatedAt = Instant.now()

        // Update components if provided
        request.patternTemplate?.let {
            updateConfigurationComponent(configUuid, PATTERN_TEMPLATE_KEY, it)
        }

        request.components?.let { components ->
            updateConfigurationComponent(configUuid, NETWORK_ID_DIGITS_KEY, components.networkId.digits.toString())
            components.networkId.value?.let {
                updateConfigurationComponent(configUuid, NETWORK_ID_VALUE_KEY, it)
            }
            updateConfigurationComponent(configUuid, FACILITY_ID_DIGITS_KEY, components.facilityId.digits.toString())
            updateConfigurationComponent(configUuid, FACILITY_ID_FORMAT_KEY, components.facilityId.format)
            updateConfigurationComponent(configUuid, SEQUENCE_DIGITS_KEY, components.sequence.digits.toString())
            updateConfigurationComponent(configUuid, SEQUENCE_FORMAT_KEY, components.sequence.format)
            updateConfigurationComponent(configUuid, SEPARATOR_KEY, components.separator)

            components.hospitalShortform?.let { shortform ->
                updateConfigurationComponent(configUuid, HOSPITAL_SHORTFORM_KEY, shortform.value ?: "")
                updateConfigurationComponent(configUuid, USE_HOSPITAL_SHORTFORM_KEY, shortform.enabled.toString())
            }
        }

        // Create history entry
        createHistoryEntry(configUuid, "UPDATE", oldValues, objectMapper.writeValueAsString(request),
                          updatedBy, request.changeReason ?: "Configuration updated")

        logger.info("UPID configuration updated successfully: $configId")
        return buildConfigurationDto(configUuid, mainConfig.code)
    }

    /**
     * Delete UPID configuration (soft delete)
     */
    @Transactional
    fun deleteConfiguration(configId: Long, deletedBy: String = "ADMIN"): Boolean {
        logger.info("Deleting UPID configuration: $configId")

        val configUuid = findConfigUuidById(configId)
        val config = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (config.sortOrder == 1) {
            throw IllegalArgumentException("Cannot delete default configuration")
        }

        val oldValues = objectMapper.writeValueAsString(getCurrentConfigurationData(configUuid))

        // Soft delete main config
        config.active = false
        config.updatedAt = Instant.now()

        // Soft delete all components
        val components = lookupValueRepository.find("category = ?1 AND active = true",
                                                   "${UPID_CONFIG_CATEGORY}_${configUuid}").list()
        components.forEach { component ->
            component.active = false
            component.updatedAt = Instant.now()
        }

        // Create history entry
        createHistoryEntry(configUuid, "DELETE", oldValues, null, deletedBy, "Configuration deleted")

        return true
    }

    /**
     * Set configuration as default
     */
    @Transactional
    fun setAsDefault(configId: Long, updatedBy: String = "ADMIN"): Boolean {
        logger.info("Setting UPID configuration as default: $configId")

        val configUuid = findConfigUuidById(configId)
        val config = lookupValueRepository.findById(configUuid)
            ?: throw IllegalArgumentException("Configuration not found: $configId")

        if (!config.active) {
            throw IllegalArgumentException("Cannot set inactive configuration as default")
        }

        // Deactivate all defaults
        deactivateAllDefaults()

        // Set this as default
        config.sortOrder = 1
        config.updatedAt = Instant.now()

        // Create history entry
        createHistoryEntry(configUuid, "SET_DEFAULT", null, "Set as default configuration",
                          updatedBy, "Configuration set as default")

        return true
    }

    /**
     * Get configuration history
     */
    fun getConfigurationHistory(configId: Long): List<Map<String, Any>> {
        val configUuid = findConfigUuidById(configId)
        val historyEntries = lookupValueRepository.find("category = ?1 AND code LIKE ?2 ORDER BY createdAt DESC",
                                                        "UPID_CONFIG_HISTORY", "${configUuid}_%").list()

        return historyEntries.map { history ->
            val parts = history.displayName.split("|")
            mapOf(
                "historyId" to history.id.toString(),
                "action" to (parts.getOrNull(0) ?: ""),
                "changedBy" to (parts.getOrNull(1) ?: ""),
                "changeReason" to (parts.getOrNull(2) ?: ""),
                "oldValues" to (parts.getOrNull(3) ?: ""),
                "newValues" to (parts.getOrNull(4) ?: ""),
                "createdAt" to OffsetDateTime.ofInstant(history.createdAt, java.time.ZoneOffset.UTC)
            )
        }
    }

    /**
     * Generate regex from template and parameters
     */
    private fun generateRegexFromTemplate(template: String, networkIdDigits: Int, facilityIdDigits: Int,
                                         sequenceDigits: Int, facilityIdFormat: String, sequenceFormat: String,
                                         separator: String): String {
        val regex = StringBuilder()
        val parts = template.split(separator)

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                regex.append("\\${separator}")
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    regex.append("\\d{$networkIdDigits}")
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    when (facilityIdFormat) {
                        "NUMERIC" -> regex.append("\\d{$facilityIdDigits}")
                        "ALPHA" -> regex.append("[A-Z]{$facilityIdDigits}")
                        "ALPHANUMERIC" -> regex.append("[A-Z0-9]{$facilityIdDigits}")
                        else -> regex.append("\\d{$facilityIdDigits}")
                    }
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (sequenceFormat == "SPLIT" && sequenceDigits >= 4) {
                        val firstPart = sequenceDigits / 2
                        val secondPart = sequenceDigits - firstPart
                        regex.append("\\d{$firstPart}\\${separator}\\d{$secondPart}")
                    } else {
                        regex.append("\\d{$sequenceDigits}")
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    regex.append("[A-Z]{3}")
                }
                else -> {
                    val escaped = Regex.escape(part)
                    regex.append(escaped)
                }
            }
        }

        return "^${regex}$"
    }

    /**
     * Generate example pattern
     */
    private fun generateExamplePattern(template: String, networkIdValue: String, facilityIdDigits: Int,
                                      sequenceDigits: Int, facilityIdFormat: String, sequenceFormat: String,
                                      separator: String, hospitalShortform: String?): String {
        val example = StringBuilder()
        val parts = template.split(separator)

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                example.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    example.append(networkIdValue)
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    when (facilityIdFormat) {
                        "NUMERIC" -> example.append("1".padStart(facilityIdDigits, '0'))
                        "ALPHA" -> example.append("A".repeat(facilityIdDigits))
                        "ALPHANUMERIC" -> example.append("A1".padEnd(facilityIdDigits, '0'))
                        else -> example.append("1".padStart(facilityIdDigits, '0'))
                    }
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (sequenceFormat == "SPLIT" && sequenceDigits >= 4) {
                        val firstPart = sequenceDigits / 2
                        val secondPart = sequenceDigits - firstPart
                        example.append("0".repeat(firstPart)).append(separator).append("1".padStart(secondPart, '0'))
                    } else {
                        example.append("1".padStart(sequenceDigits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    example.append(hospitalShortform ?: "HSP")
                }
                else -> {
                    example.append(part)
                }
            }
        }

        return example.toString()
    }

    /**
     * Generate pattern information from template and components
     */
    private fun generatePatternFromTemplate(template: String, components: UpidComponentsDto): PatternInfo {
        val regex = generateRegexFromTemplate(template, components.networkId.digits, components.facilityId.digits,
                                             components.sequence.digits, components.facilityId.format,
                                             components.sequence.format, components.separator)

        val example = generateExamplePattern(template, components.networkId.value ?: "00", components.facilityId.digits,
                                           components.sequence.digits, components.facilityId.format,
                                           components.sequence.format, components.separator,
                                           components.hospitalShortform?.value)

        val breakdown = mutableMapOf<String, String>()
        breakdown["network"] = "Network ID (${components.networkId.digits} digits)"
        breakdown["facility"] = "Facility ID (${components.facilityId.digits} digits, ${components.facilityId.format})"
        breakdown["sequence"] = "Sequence (${components.sequence.digits} digits, ${components.sequence.format} format)"
        if (components.hospitalShortform?.enabled == true) {
            breakdown["hospital"] = "Hospital shortform (${components.hospitalShortform.value?.length ?: 3} characters)"
        }

        return PatternInfo(
            regex = regex,
            validationRegex = regex,
            example = example,
            breakdown = breakdown
        )
    }

    /**
     * Generate sample UPID for validation
     */
    private fun generateSampleUpid(facilityId: String, sequence: Long, template: String, components: UpidComponentsDto): String {
        val separator = components.separator
        val parts = template.split(separator)
        val result = StringBuilder()

        parts.forEachIndexed { index, part ->
            if (index > 0) {
                result.append(separator)
            }

            when {
                part.contains("NETWORK") || part.contains("NN") -> {
                    result.append((components.networkId.value ?: "00").padStart(components.networkId.digits, '0'))
                }
                part.contains("FACILITY") || part.contains("FFF") || part.contains("XXX") -> {
                    val paddedFacilityId = when (components.facilityId.format) {
                        "NUMERIC" -> facilityId.padStart(components.facilityId.digits, '0')
                        "ALPHA" -> facilityId.uppercase().padStart(components.facilityId.digits, 'A')
                        "ALPHANUMERIC" -> facilityId.uppercase().padStart(components.facilityId.digits, '0')
                        else -> facilityId.padStart(components.facilityId.digits, '0')
                    }
                    result.append(paddedFacilityId)
                }
                part.contains("SEQUENCE") || part.contains("NNNN") -> {
                    if (components.sequence.format == "SPLIT" && components.sequence.digits >= 4) {
                        val paddedSequence = sequence.toString().padStart(components.sequence.digits, '0')
                        val firstPart = components.sequence.digits / 2
                        val secondPart = components.sequence.digits - firstPart
                        result.append(paddedSequence.substring(0, firstPart))
                        result.append(separator)
                        result.append(paddedSequence.substring(firstPart))
                    } else {
                        result.append(sequence.toString().padStart(components.sequence.digits, '0'))
                    }
                }
                part.contains("HOSPITAL") || part.contains("HHH") -> {
                    result.append(components.hospitalShortform?.value ?: "HSP")
                }
                else -> {
                    result.append(part)
                }
            }
        }

        return result.toString()
    }

    /**
     * Data class for pattern information
     */
    private data class PatternInfo(
        val regex: String,
        val validationRegex: String,
        val example: String,
        val breakdown: Map<String, String>
    )
}
