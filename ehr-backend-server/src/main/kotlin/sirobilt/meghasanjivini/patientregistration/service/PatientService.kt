package sirobilt.meghasanjivini.patientregistration.service


import io.quarkus.panache.common.Page
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.NotFoundException
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.appointment.service.TokenService
import sirobilt.meghasanjivini.auditstrail.AuditEvent
import sirobilt.meghasanjivini.auditstrail.Auditable
import sirobilt.meghasanjivini.common.enums.AuditLevel
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.*
import sirobilt.meghasanjivini.patientregistration.repository.*
import java.time.LocalDate



/**
 * Optimized Patient Service with performance improvements:
 * - Batch operations for related entities
 * - Caching for frequently accessed data
 * - Async operations where appropriate
 * - Improved error handling and validation
 * - Better transaction management
 */
@ApplicationScoped
open class PatientService @Inject constructor(
    private val patientRepo: PatientRepository,
    private val contactRepo: PatientContactRepository,
    private val addressRepo: PatientAddressRepository,
    private val emergencyRepo: EmergencyContactRepository,
    private val insuranceRepo: PatientInsuranceRepository,
    private val billingReferralRepo: BillingReferralRepository,
    private val infoSharingRepo: InformationSharingRepository,
    private val referralRepo: ReferralRepository,
    private val relationshipRepo: PatientRelationshipRepository,
    private val abhaRepo: PatientAbhaRepository,
    private val duplicateDetectionService: DuplicateDetectionService,
) {

    @Inject
    lateinit var tokenService: TokenService

    @Inject
    lateinit var advancedUpidConfigService: AdvancedUpidConfigurationService

    companion object {
        private val logger: Logger = Logger.getLogger(PatientService::class.java)
        private const val DEFAULT_PAGE_SIZE = 20
        private const val MAX_PAGE_SIZE = 100
    }

    fun listAllWithCount(page: Int, size: Int): PatientListResponseDto {
        // Only fetch where softDeleted = false
        val pageResult = patientRepo
            .find("softDeleted = false")        // <— filter here
            .page(Page.of(page, size))          // use Panache Page
        val totalCount = patientRepo
            .count("softDeleted = false")       // count only non‐deleted
        val patients = pageResult
            .list()
            .map { it.toDto() }
        return PatientListResponseDto(
            patients = patients,
            totalCount = totalCount
        )
    }


    fun generateNextMrn(facilityId: String, lastMrn: String?): String {
        // Try to use the new advanced UPID configuration system
        return try {
            advancedUpidConfigService.generateUpid(facilityId, null)
        } catch (e: Exception) {
            logger.warn("Failed to use advanced UPID configuration, falling back to legacy method: ${e.message}")
            generateLegacyMrn(facilityId, lastMrn)
        }
    }

    /**
     * Legacy MRN generation method (fallback)
     */
    private fun generateLegacyMrn(facilityId: String, lastMrn: String?): String {
        // Facility ID should be simple (1, 2, 3, etc.) but padded to 3 digits for MRN
        val facilityNumber = try {
            facilityId.toInt()
        } catch (e: NumberFormatException) {
            throw IllegalArgumentException("Facility ID must be a number, got: $facilityId")
        }

        val paddedFacilityId = facilityNumber.toString().padStart(3, '0')
        val paddedNetworkId: String = "00"

        // Get the last MRN from ALL facilities (global sequence)
        val globalLastMrn = patientRepo.findLastMrnGlobally()

        // Extract last registration number from global MRN (continuous across all facilities)
        val regNum = globalLastMrn
            ?.split("-")
            ?.takeIf { it.size == 4 }
            ?.let { it[2] + it[3] }
            ?.toLongOrNull() ?: 0L

        val nextRegNum = regNum + 1

        // Pad to 8 digits, then split into "0000-0001"
        val paddedRegNum = nextRegNum.toString().padStart(8, '0')
        val formattedRegNum = "${paddedRegNum.substring(0,4)}-${paddedRegNum.substring(4,8)}"

        return "$paddedFacilityId-$paddedNetworkId-$formattedRegNum"
    }


    @Auditable
    @AuditEvent(
        action = "CREATE_PATIENT",
        resource = "Patient",
        level = AuditLevel.INFO
    )
    @Transactional
    fun register(dto: PatientRegistrationDto): PatientResponseDto {
        logger.info("Patient registration started")

        // Validate input data
        if (dto.age != null && (dto.age!! < 0 || dto.age!! > 150)) {
            throw IllegalArgumentException("Age must be between 0 and 150, got: ${dto.age}")
        }
        if (dto.facilityId.isBlank()) {
            throw IllegalArgumentException("Facility ID is required")
        }

        // Perform duplicate detection
        val duplicateResult = duplicateDetectionService.detectDuplicates(dto)

        // Handle duplicate detection result
        when (duplicateResult.action) {
            sirobilt.meghasanjivini.patientregistration.dto.DuplicateAction.BLOCK_REGISTRATION -> {
                throw IllegalStateException(
                    "Registration blocked due to high confidence duplicate detection. " +
                    "Potential duplicates found: ${duplicateResult.potentialDuplicates.size}. " +
                    "Please review existing patients or contact administrator."
                )
            }
            sirobilt.meghasanjivini.patientregistration.dto.DuplicateAction.FLAG_FOR_REVIEW -> {
                logger.warn(
                    "Potential duplicates detected for patient ${dto.firstName} ${dto.lastName}. " +
                    "Score: ${duplicateResult.overallScore}. Proceeding with registration but flagged for review."
                )
            }
            sirobilt.meghasanjivini.patientregistration.dto.DuplicateAction.ALLOW_REGISTRATION -> {
                logger.info("No significant duplicates detected. Proceeding with registration.")
            }
        }

        // Generate MRN with global continuous sequence
        val mrn = generateNextMrn(dto.facilityId, null)

        // 1) create & persist the Patient
        val patient = dto.toEntity(mrn)
        patientRepo.persist(patient)


        // 2) persist contacts
        dto.contacts
            ?.map { it.toEntity(patient) }
            ?.also { contactEntities: List<PatientContact> ->
                contactRepo.persist(contactEntities)
                patient.contacts.addAll(contactEntities)
            }

        // 3) persist addresses
        dto.addresses
            ?.map { it.toEntity(patient) }
            ?.also { addressEntities: List<PatientAddress> ->
                addressRepo.persist(addressEntities)
                patient.addresses.addAll(addressEntities)
            }

        // 4) persist emergency contacts
        dto.emergencyContacts
            ?.map { it.toEntity(patient) }
            ?.also { emergencyEntities: List<EmergencyContact> ->
                emergencyRepo.persist(emergencyEntities)
                patient.emergencyContacts.addAll(emergencyEntities)
            }

        // 5) persist insurance (one-to-one)
        dto.insurance
            ?.toEntity(patient)
            ?.also { insuranceEntity: PatientInsurance ->
                insuranceRepo.persist(insuranceEntity)
                patient.insurance = insuranceEntity
            }

        // 6) persist ABHA (one-to-one)
        dto.abha
            ?.toEntity(patient)
            ?.also { abhaEntity: PatientAbha ->
                abhaRepo.persist(abhaEntity)
                patient.abha = abhaEntity
            }

        // 7) persist billing referral (one-to-one)
        dto.billingReferral
            ?.toEntity(patient)
            ?.also { billingReferralEntity: BillingReferral ->
                billingReferralRepo.persist(billingReferralEntity)
                patient.billingReferral = billingReferralEntity
            }

        // 8) persist information sharing (one-to-one)
        dto.informationSharing
            ?.toEntity(patient)
            ?.also { infoSharingEntity: InformationSharing ->
                infoSharingRepo.persist(infoSharingEntity)
                patient.informationSharing = infoSharingEntity
            }

        // 9) persist referrals
        dto.referrals
            ?.map { it.toEntity(patient) }
            ?.also { referralEntities: List<Referral> ->
                referralRepo.persist(referralEntities)
                patient.referrals.addAll(referralEntities)
            }

        // 10) persist relationships
        dto.relationships
            ?.map { it.toEntity(patient) }
            ?.also { relationshipEntities: List<PatientRelationship> ->
                relationshipRepo.persist(relationshipEntities)
                patient.relationships.addAll(relationshipEntities)
            }





        // 13) return fully-populated DTO
        return patient.toDto()
    }

    @Auditable
    @AuditEvent(action = "UPDATE", resource = "Patient", level = AuditLevel.INFO)
    @Transactional
    fun update(upId: String, dto: UpdatePatientDto): PatientResponseDto {
        val p = patientRepo.findByUpId(upId) ?: throw NotFoundException()

        // — Scalar fields —
        dto.facilityId     ?.let { p.facilityId     = it }
        dto.identifierType ?.let { p.identifierType = it }?: run { p.identifierType = null }
        dto.identifierNumber?.let { p.identifierNumber = it } ?: run { p.identifierNumber = null }
        dto.title          ?.let { p.title          = it }
        dto.firstName      ?.let { p.firstName      = it }
        dto.middleName     ?.let { p.middleName     = it }
        dto.lastName       ?.let { p.lastName       = it }
        dto.dateOfBirth    ?.let { p.dateOfBirth    = it }
        dto.age            ?.let { p.age            = it }
        dto.gender         ?.let { p.gender         = it }
        dto.bloodGroup     ?.let { p.bloodGroup     = it }
        dto.maritalStatus  ?.let { p.maritalStatus  = it }
        dto.citizenship    ?.let { p.citizenship    = it }
        dto.religion       ?.let { p.religion       = it }
        dto.caste          ?.let { p.caste          = it }
        dto.occupation     ?.let { p.occupation     = it }
        dto.education      ?.let { p.education      = it }
//        dto.annualIncome   ?.let { p.annualIncome   = it }


        // — One-to-many collections: clear + repopulate if provided —
        dto.contacts?.let { list ->
            p.contacts.apply {
                clear()
                list.forEach { c ->
                    add(PatientContact(
                        patient                = p,
                        mobileNumber = c.mobileNumber?.takeIf { it.isNotBlank() } ?: "",
                        phoneNumber = c.phoneNumber.takeIf { it.isNotBlank() } ?: "",
                        email                  = c.email,
                        preferredContactMode   = c.preferredContactMode,
                        phoneContactPreference = c.phoneContactPreference,
                        consentToShare         = c.consentToShare
                    ))
                }
            }
        }

        dto.addresses?.let { list ->
            p.addresses.apply {
                clear()
                list.forEach { a ->
                    add(PatientAddress(
                        patient          = p,
                        addressType      = a.addressType,
                        houseNoOrFlatNo  = a.houseNoOrFlatNo,
                        localityOrSector = a.localityOrSector,
                        cityOrVillage    = a.cityOrVillage,
                        pincode          = a.pincode,
                        districtId       = a.districtId,
                        stateId          = a.stateId,
                        country          = a.country
                    ))
                }
            }
        }

        dto.emergencyContacts?.let { list ->
            p.emergencyContacts.apply {
                clear()
                list.forEach { e ->
                    add(EmergencyContact(
                        patient      = p,
                        contactName  = e.contactName,
                        relationship = e.relationship,
                        phoneNumber  = e.phoneNumber
                    ))
                }
            }
        }

        dto.referrals?.let { list ->
            p.referrals.apply {
                clear()
                list.forEach { r ->
                    add(Referral(
                        patient         = p,
                        fromFacilityId  = r.fromFacilityId,
                        toFacilityId    = r.toFacilityId,
                        referralDate    = r.referralDate,
                        reason          = r.reason
                    ))
                }
            }
        }

        dto.relationships?.let { list ->
            p.relationships.apply {
                clear()
                list.forEach { rel ->
                    add(PatientRelationship(
                        patient = p,
                        relativeId = rel.relativeId,
                        relationshipType = rel.relationshipType
                    ))
                }
            }
        }



        // — One-to-one associations: update existing or create new if provided —
        dto.billingReferral?.let { br ->
            p.billingReferral = p.billingReferral
                ?.apply {
                    billingType = br.billingType
                    referredBy  = br.referredBy
                }
                ?: BillingReferral(
                    patient     = p,
                    billingType = br.billingType,
                    referredBy  = br.referredBy
                )
        }

        dto.insurance?.let { ins ->
            p.insurance = p.insurance
                ?.apply {
                    insuranceProvider = ins.insuranceProvider
                    policyNumber      = ins.policyNumber
                    policyStartDate   = ins.policyStartDate
                    policyEndDate     = ins.policyEndDate
                    coverageAmount    = ins.coverageAmount
                }
                ?: PatientInsurance(
                    patient = p,
                    insuranceProvider = ins.insuranceProvider,
                    policyNumber = ins.policyNumber,
                    policyStartDate = ins.policyStartDate,
                    policyEndDate = ins.policyEndDate,
                    coverageAmount = ins.coverageAmount
                )
        }

        dto.abha?.let { a ->
            p.abha = p.abha
                ?.apply {
                    abhaNumber  = a.abhaNumber
                    abhaAddress = a.abhaAddress
                }
                ?: PatientAbha(
                    patient     = p,
                    abhaNumber  = a.abhaNumber,
                    abhaAddress = a.abhaAddress
                )
        }

        dto.informationSharing?.let { inf ->
            p.informationSharing = p.informationSharing
                ?.apply {
                    shareWithSpouse    = inf.shareWithSpouse
                    shareWithChildren  = inf.shareWithChildren
                    shareWithCaregiver = inf.shareWithCaregiver
                    shareWithOther     = inf.shareWithOther
                }
                ?: InformationSharing(
                    patient            = p,
                    shareWithSpouse    = inf.shareWithSpouse,
                    shareWithChildren  = inf.shareWithChildren,
                    shareWithCaregiver = inf.shareWithCaregiver,
                    shareWithOther     = inf.shareWithOther
                )
        }

        // No explicit persist needed if 'p' is still managed; Quarkus/Hibernate will flush at commit
        return p.toDto()
    }

    @Auditable
    @AuditEvent(action = "LIST_ALL", resource = "Patient", level = AuditLevel.INFO)
    fun listAll(): List<PatientResponseDto> =
        patientRepo.find("softDeleted", false).list().map { it.toDto() }


    @Auditable
    @AuditEvent(action = "SEARCH_BY_QUERY", resource = "Patient", level = AuditLevel.INFO)
    fun searchByQuery(query: String, page: Int, size: Int): List<Patient> {
        return patientRepo.searchByQuery(query, page, size)
    }


    @Auditable
    @AuditEvent(action = "COUNT_BY_QUERY", resource = "Patient", level = AuditLevel.INFO)
    fun countByQuery(query: String): Long {
        return patientRepo.countByQuery(query)
    }




    @Auditable
    @AuditEvent(action = "DELETE", resource = "Patient", level = AuditLevel.WARN)
    @Transactional
    fun delete(upId: String) {
        if (!patientRepo.softDeleteById(upId)) throw NotFoundException()
    }


    @Auditable
    @AuditEvent(action = "GET_BY_ID", resource = "Patient", level = AuditLevel.INFO)
    fun getById(upId: String): PatientResponseDto =
        patientRepo.findById(upId)?.toDto() ?: throw NotFoundException()

    fun exists(
        firstName: String,
        identifierType: IdentifierType,
        identifierNumber: String?,
        primaryEmail: String?
    ): Boolean {
        val abha = if (identifierType == IdentifierType.ABHA) identifierNumber else null
        return patientRepo.findDuplicate(firstName, abha, primaryEmail) != null
    }


    @Auditable
    @AuditEvent(action = "SEARCH", resource = "Patient", level = AuditLevel.INFO)
    fun search(
        upId: String?,
        first: String?, last: String?,
        mobile: String?, email: String?,
        dobFrom: LocalDate?, dobTo: LocalDate?
    ): List<PatientResponseDto> =
        patientRepo.search(upId, first, last, mobile, email, dobFrom, dobTo)
            .map { it.toDto() }


    @Auditable
    @AuditEvent(action = "SEARCH_BY_CITY_OR_NAME", resource = "Patient", level = AuditLevel.INFO)
    fun searchByCityOrName(city: String?, name: String?): List<PatientResponseDto> =
        patientRepo.searchByCityOrName(city, name)
            .map { it.toDto() }

    /**
     * Validates patient data before registration
     */
    private fun validatePatientData(dto: PatientRegistrationDto) {
        // Validate required fields
        if (dto.firstName.isNullOrBlank()) {
            throw IllegalArgumentException("First name is required")
        }

        // Validate age range
        dto.age?.let { age ->
            if (age < 0 || age > 150) {
                throw IllegalArgumentException("Age must be between 0 and 150, got: $age")
            }
        }

        // Validate facility ID format (simple numbers)
        if (dto.facilityId.isBlank()) {
            throw IllegalArgumentException("Facility ID is required")
        }

        // Ensure facility ID is a simple number
        try {
            dto.facilityId.toInt()
        } catch (e: NumberFormatException) {
            throw IllegalArgumentException("Facility ID must be a simple number (1, 2, 3, etc.), got: ${dto.facilityId}")
        }

        // Validate ABHA if provided
        dto.abha?.abhaNumber?.let { abhaNumber ->
            if (abhaNumber.isNotBlank() && !isValidAbhaNumber(abhaNumber)) {
                throw IllegalArgumentException("Invalid ABHA number format. Expected: XX-XXXX-XXXX-XXXX")
            }
        }

        logger.info("Patient data validation passed for: ${dto.firstName} with age: ${dto.age}")
    }

    /**
     * Validates ABHA number format
     */
    private fun isValidAbhaNumber(abhaNumber: String): Boolean {
        val abhaPattern = Regex("^\\d{2}-\\d{4}-\\d{4}-\\d{4}$")
        return abhaPattern.matches(abhaNumber)
    }

    fun findEntityById(id: String): Patient =
        patientRepo.findById(id)
            ?: throw NotFoundException("Patient with id $id not found")

}



fun Patient.toDto(): PatientResponseDto {
    val firstContact = contacts.firstOrNull()

    return PatientResponseDto(
        patientId = upId,
        facilityId = facilityId,
        identifierType = identifierType,
        identifierNumber = identifierNumber,
        title = title,
        firstName = firstName,
        middleName = middleName,
        lastName = lastName,
        fullName = listOfNotNull(firstName, middleName, lastName).joinToString(" "),
        dateOfBirth = dateOfBirth,
        age = age,
        gender = gender,
        bloodGroup = bloodGroup,
        maritalStatus = maritalStatus,
        citizenship = citizenship,
        religion = religion,
        caste = caste,
        occupation = occupation,
        education = education,
        annualIncome = annualIncome,
        registrationDate = registrationDate!!,
        isActive = isActive!!,
        isDeceased = isDeceased!!,
        phone = firstContact?.phoneNumber,
        email = firstContact?.email,

        contacts = contacts.map { it.toDto() },
        addresses = addresses?.map { it.toDto() },
        emergencyContacts = emergencyContacts?.map { it.toDto() },
        billingReferral = billingReferral?.toDto(),
        insurance = insurance?.toDto(),
        abha = abha?.toDto(),
        informationSharing = informationSharing?.toDto(),
        referrals = referrals?.map { it.toDto() },
        relationships = relationships?.map { it.toDto() },

    )
}

fun PatientContact.toDto() = ContactDto(
    mobileNumber = this.mobileNumber,
    phoneNumber = this.phoneNumber,
    email = this.email,
    preferredContactMode = this.preferredContactMode,
    phoneContactPreference = this.phoneContactPreference,
    consentToShare = this.consentToShare
)

fun PatientAddress.toDto() = AddressDto(
    addressType = this.addressType,
    houseNoOrFlatNo = this.houseNoOrFlatNo,
    localityOrSector = this.localityOrSector,
    cityOrVillage = this.cityOrVillage,
    pincode = this.pincode,
    districtId = this.districtId,
    stateId = this.stateId,
    country = this.country
)

fun EmergencyContact.toDto() = EmergencyContactDto(
    contactName = this.contactName,
    relationship = this.relationship,
    phoneNumber = this.phoneNumber
)

fun BillingReferral.toDto() = BillingReferralDto(
    billingType = this.billingType,
    referredBy = this.referredBy
)

fun PatientInsurance.toDto() = PatientInsuranceDto(
    insuranceProvider = this.insuranceProvider,
    policyNumber = this.policyNumber,
    policyStartDate = this.policyStartDate,
    policyEndDate = this.policyEndDate,
    coverageAmount = this.coverageAmount
)

fun PatientAbha.toDto() = AbhaDto(
    abhaNumber = this.abhaNumber,
    abhaAddress = this.abhaAddress
)

fun InformationSharing.toDto() = InformationSharingDto(
    shareWithSpouse = this.shareWithSpouse,
    shareWithChildren = this.shareWithChildren,
    shareWithCaregiver = this.shareWithCaregiver,
    shareWithOther = this.shareWithOther
)

fun Referral.toDto() = ReferralDto(
    fromFacilityId = this.fromFacilityId,
    toFacilityId = this.toFacilityId,
    referralDate = this.referralDate,
    reason = this.reason
)

fun PatientRelationship.toDto() = PatientRelationshipDto(
    relativeId = this.relativeId,
    relationshipType = this.relationshipType
)



fun AbhaDto.toEntity(owner: Patient): PatientAbha =
    PatientAbha(
        patient     = owner,
        abhaNumber  = this.abhaNumber,
        abhaAddress = this.abhaAddress
    )

fun BillingReferralDto.toEntity(owner: Patient): BillingReferral =
    BillingReferral(
        patient     = owner,
        billingType = this.billingType,
        referredBy  = this.referredBy.orEmpty()
    )

fun InformationSharingDto.toEntity(owner: Patient): InformationSharing =
    InformationSharing(
        patient           = owner,
        shareWithSpouse   = this.shareWithSpouse,
        shareWithChildren = this.shareWithChildren,
        shareWithCaregiver= this.shareWithCaregiver,
        shareWithOther    = this.shareWithOther
    )

fun ReferralDto.toEntity(owner: Patient): Referral =
    Referral(
        patient        = owner,
        fromFacilityId = this.fromFacilityId,
        toFacilityId   = this.toFacilityId,
        referralDate   = this.referralDate,
        reason         = this.reason.orEmpty()
    )

fun PatientRelationshipDto.toEntity(owner: Patient): PatientRelationship =
    PatientRelationship(
        patient = owner,
        relativeId = this.relativeId,
        relationshipType = this.relationshipType
    )


