package sirobilt.meghasanjivini.patientregistration.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.persistence.EntityManager
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.Granularity

@ApplicationScoped
class RegistrationDashboardService {

    @Inject
    lateinit var em: EntityManager

    @Transactional
    fun fetchCounts(req: RegistrationDashboardRequest): List<PeriodCount> {
        val dateFormat = when (req.granularity) {
            Granularity.day   -> "YYYY-MM-DD"
            Granularity.month -> "YYYY-MM"
            Granularity.year  -> "YYYY"
        }

        val sql = """
            SELECT 
              to_char(registration_date::date, '$dateFormat') AS period,
              COUNT(*) 
            FROM patients
            WHERE registration_date::date BETWEEN :fromDate AND :toDate
            GROUP BY period
            ORDER BY period
        """

        @Suppress("UNCHECKED_CAST")
        val rows = em.createNativeQuery(sql)
            .setParameter("fromDate", req.fromDate)
            .setParameter("toDate", req.toDate)
            .resultList as List<Array<Any>>

        return rows.map { (period, count) ->
            PeriodCount(period as String, (count as Number).toLong())
        }
    }
}