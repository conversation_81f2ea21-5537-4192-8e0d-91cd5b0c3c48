package sirobilt.meghasanjivini.waitingroom.controller

import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import sirobilt.meghasanjivini.waitingroom.service.DepartmentInfoService

@Path("/waiting-room/departments")
@Produces(MediaType.APPLICATION_JSON)
class DepartmentController {

    @Inject
    lateinit var departmentInfoService: DepartmentInfoService

    @GET
    fun getDepartments(): Response {
        val result = departmentInfoService.getAllDepartments()
        return Response.ok(result).build()
    }
}
