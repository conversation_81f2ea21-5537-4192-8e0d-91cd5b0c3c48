package sirobilt.meghasanjivini.waitingroom.controller

import com.arjuna.ats.arjuna.logging.tsLogger.logger
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import sirobilt.meghasanjivini.waitingroom.service.DoctorAvailabilityService
import sirobilt.meghasanjivini.waitingroom.service.DoctorStatusService

@Path("/waiting-room/doctors")
@Produces(MediaType.APPLICATION_JSON)
class DoctorAvailabilityController {

    @Inject
    lateinit var service: DoctorAvailabilityService

    @Inject
    lateinit var doctorStatusService: DoctorStatusService

    @GET
    @Path("/today")
    fun getTodayAvailability(): Response {
        logger.info("Fetching today's doctor availability")
        val result = service.getDoctorAvailabilityToday()
        return Response.ok(result).build()
    }

    @GET
    @Path("status")
    fun getStatus(): Response {
        val result = doctorStatusService.getRealTimeStatus()
        return Response.ok(result).build()
    }
}
