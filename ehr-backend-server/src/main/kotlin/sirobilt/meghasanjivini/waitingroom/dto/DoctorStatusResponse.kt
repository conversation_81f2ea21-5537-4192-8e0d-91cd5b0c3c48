package sirobilt.meghasanjivini.waitingroom.dto

import java.time.LocalTime
import java.util.UUID

/**
 * API Response for GET /api/waiting-room/doctors/status
 */
data class DoctorStatusResponse(
    val success: Boolean,
    val timestamp: String,
    val data: StatusData
) {
    data class StatusData(
        val lastUpdated: String,
        val updates: List<DoctorStatusUpdate>
    )
}

data class DoctorStatusUpdate(
    val doctorId: UUID,
    val status: String, // Available | Busy | Break | Offline | Emergency
    val currentPatientCount: Int? = null,
    val estimatedWaitTime: Int? = null,
    val nextAvailableTime: String? = null,
    val lastStatusChange: String
)
