package sirobilt.meghasanjivini.waitingroom.service

import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.department.repository.DepartmentRepository
import sirobilt.meghasanjivini.department.service.DepartmentOperatingHoursService
import sirobilt.meghasanjivini.department.service.DepartmentServiceService
import sirobilt.meghasanjivini.waitingroom.dto.*

@ApplicationScoped
class DepartmentInfoService(
    private val departmentRepository: DepartmentRepository,
    private val departmentServiceService: DepartmentServiceService,
    private val operatingHoursService: DepartmentOperatingHoursService
) {
    fun getAllDepartments(): DepartmentResponse {
        val departments = departmentRepository.list("isActive", true)

        val infos = departments.map { dept ->
            val services = departmentServiceService.getByDepartment(dept.departmentId).map { it.serviceName }
            val hours = operatingHoursService.getByDepartment(dept.departmentId)

            val weekdayHours = hours.filter { it.dayOfWeek.value in 1..5 }
            val weekendHours = hours.filter { it.dayOfWeek.value in 6..7 }

            DepartmentInfo(
                id = dept.departmentId,
                name = dept.name,
                code = dept.code,
                description = dept.description,
                color = "#4285F4",
                location = DepartmentLocation(
                    floor = dept.location ?: "",
                    wing = null,
                    directions = null
                ),
                services = services,
                operatingHours = OperatingHours(
                    weekdays = weekdayHours.firstOrNull()?.let {
                        TimeRange(it.startTime.toString(), it.endTime.toString())
                    } ?: TimeRange("09:00", "17:00"),
                    weekends = weekendHours.firstOrNull()?.let {
                        TimeRange(it.startTime.toString(), it.endTime.toString())
                    },
                    holidays = null
                ),
                emergencyAvailable = dept.isEmergencyDepartment,
                contactInfo = ContactInfo(
                    phone = dept.phoneNumber,
                    extension = null
                )
            )
        }

        return DepartmentResponse(
            success = true,
            data = DepartmentResponse.Data(departments = infos)
        )
    }
}
