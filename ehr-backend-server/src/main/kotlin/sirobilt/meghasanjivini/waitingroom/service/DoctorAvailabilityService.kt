package sirobilt.meghasanjivini.waitingroom.service

import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.masterdata.repository.DoctorRepository
import sirobilt.meghasanjivini.department.repository.ProviderDepartmentMappingRepository
import sirobilt.meghasanjivini.department.repository.DepartmentRepository
import org.acme.appointmentconfig.repositories.AppointmentSlotRepo
import sirobilt.meghasanjivini.common.enums.SlotAvailability
import sirobilt.meghasanjivini.waitingroom.dto.*
import java.time.LocalDate
import java.util.UUID

@ApplicationScoped
class DoctorAvailabilityService(
    private val doctorRepository: DoctorRepository,
    private val mappingRepository: ProviderDepartmentMappingRepository,
    private val departmentRepository: DepartmentRepository,
    private val slotRepo: AppointmentSlotRepo
) {

    fun getDoctorAvailabilityToday(): DoctorAvailabilityGroupResponse {
        val today = LocalDate.now()
        println("🟡 [DoctorAvailabilityService] Fetching availability for date: $today")

        val mappings = mappingRepository.list("isActive", true)
        println("🔍 Total active provider-department mappings: ${mappings.size}")

        val grouped = mappings.groupBy { it.departmentId }

        val result = grouped.mapNotNull { (deptId, doctorMappings) ->
            println("➡️ Processing Department: $deptId with ${doctorMappings.size} doctor mappings")

            val department = departmentRepository.findById(deptId)
            if (department == null) {
                println("⚠️ Skipping department: $deptId — not found in DB")
                return@mapNotNull null
            }

            val doctors = doctorMappings.mapNotNull { mapping ->
                println("🧠 Fetching doctor for providerId: ${mapping.providerId}")

                val doctor = doctorRepository.find("doctorId", UUID.fromString(mapping.providerId)).firstResult()
                if (doctor == null) {
                    println("❌ No doctor found for providerId: ${mapping.providerId}")
                    return@mapNotNull null
                }

                val slots = slotRepo.find(
                    "consultantId = ?1 and slotDate = ?2",
                    doctor.doctorId, today
                ).list()

                println("⏳ Found ${slots.size} slots for Dr. ${doctor.fullName} on $today")

                if (slots.isEmpty()) {
                    println("⚠️ No slots available for Dr. ${doctor.fullName}, skipping")
                    return@mapNotNull null
                }

                val openSlots = slots.filter { it.availability == SlotAvailability.OPEN }

                println("✅ ${openSlots.size} open slots for Dr. ${doctor.fullName}")

                DoctorSlotDTO(
                    doctorId = doctor.doctorId,
                    fullName = doctor.fullName,
                    isAvailable = openSlots.isNotEmpty(),
                    nextAvailableTime = openSlots.minByOrNull { it.startTime }?.startTime,
                    totalSlots = slots.size,
                    openSlots = openSlots.size,
                    experience = doctor.yearsOfExperience,
                    languages = doctor.languagesSpoken
                )
            }

            println("✅ Department '${department.name}' has ${doctors.size} doctor(s) with valid slots")

            if (doctors.isEmpty()) {
                println("⚠️ No valid doctors for department '${department.name}', skipping department")
                return@mapNotNull null
            }

            DoctorDepartmentGroup(
                departmentId = department.departmentId,
                departmentName = department.name,
                totalDoctors = doctors.size,
                availableDoctors = doctors.count { it.isAvailable },
                doctors = doctors
            )
        }

        println("✅ Finished doctor availability aggregation. Departments returned: ${result.size}")

        return DoctorAvailabilityGroupResponse(
            date = today.toString(),
            departments = result
        )
    }
}
