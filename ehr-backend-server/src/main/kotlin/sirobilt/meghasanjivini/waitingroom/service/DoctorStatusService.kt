package sirobilt.meghasanjivini.waitingroom.service

import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlot
import sirobilt.meghasanjivini.common.enums.SlotAvailability
import sirobilt.meghasanjivini.department.repository.ProviderDepartmentMappingRepository
import sirobilt.meghasanjivini.masterdata.repository.DoctorRepository
import sirobilt.meghasanjivini.waitingroom.dto.DoctorStatusResponse
import sirobilt.meghasanjivini.waitingroom.dto.DoctorStatusUpdate
import org.acme.appointmentconfig.repositories.AppointmentSlotRepo
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*

@ApplicationScoped
class DoctorStatusService(
    private val doctorRepo: DoctorRepository,
    private val mappingRepo: ProviderDepartmentMappingRepository,
    private val slotRepo: AppointmentSlotRepo
) {

    fun getRealTimeStatus(): DoctorStatusResponse {
        val now = Instant.now()
        val today = LocalDate.now()
        println("🟡 [DoctorStatusService] Checking doctor real-time status for: $today")

        val mappings = mappingRepo.list("isActive", true)
        println("🔍 Found ${mappings.size} active provider-department mappings")

        val updates = mappings.mapNotNull { mapping ->
            println("➡️ Mapping: providerId=${mapping.providerId}, departmentId=${mapping.departmentId}")

            val doctor = try {
                val uuid = UUID.fromString(mapping.providerId)
                doctorRepo.find("doctorId", uuid).firstResult()
            } catch (e: IllegalArgumentException) {
                println("❌ Skipping invalid UUID providerId: ${mapping.providerId}")
                return@mapNotNull null
            }

            if (doctor == null) {
                println("⚠️ No doctor found for providerId: ${mapping.providerId}")
                return@mapNotNull null
            }

            println("✅ Doctor found: ${doctor.fullName} [${doctor.doctorId}]")

            val slots = slotRepo.find(
                "consultantId = ?1 and slotDate = ?2",
                doctor.doctorId, today
            ).list()

            println("⏳ Found ${slots.size} slots for Dr. ${doctor.fullName} on $today")

            if (slots.isEmpty()) {
                println("⚠️ No slots found for Dr. ${doctor.fullName}")
                return@mapNotNull null
            }

            val openSlots = slots.filter { it.availability == SlotAvailability.OPEN }
            val busySlots = slots.filter { it.availability == SlotAvailability.BOOKED }
            val breakSlots = slots.filter { it.availability == SlotAvailability.BLOCKED }

            val status: String = when {
                busySlots.isNotEmpty() -> "Busy"
                breakSlots.isNotEmpty() -> "Break"
                openSlots.isNotEmpty() -> "Available"
                else -> "Offline"
            }

            println("📊 Status for Dr. ${doctor.fullName}: $status (${busySlots.size} busy, ${openSlots.size} open, ${breakSlots.size} break)")

            DoctorStatusUpdate(
                doctorId = doctor.doctorId!!,
                status = status,
                currentPatientCount = busySlots.size.takeIf { it > 0 },
                estimatedWaitTime = busySlots.size * 15, // assuming 15min per patient
                nextAvailableTime = openSlots.minByOrNull { it.startTime }?.startTime?.toString(),
                lastStatusChange = now.toString()
            )
        }

        println("✅ Completed real-time doctor status aggregation. Updates: ${updates.size}")

        return DoctorStatusResponse(
            success = true,
            timestamp = now.toString(),
            data = DoctorStatusResponse.StatusData(
                lastUpdated = now.toString(),
                updates = updates
            )
        )
    }
}
