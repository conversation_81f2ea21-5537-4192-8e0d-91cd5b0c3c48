-- Migration V5: Add UPID Configuration Lookup Values (Flattened Structure)
-- Description: Add UPID configuration data to the existing lookup_values table using flattened structure

-- Insert default UPID configuration (Standard Format) - Main Entry
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
VALUES (
    RANDOM_UUID(),
    'UPID_CONFIG',
    'STANDARD_UPID_FORMAT',
    'Standard UPID format with facility prefix, network ID, and split sequence number',
    1, -- Default configuration (sort_order = 1)
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert components for Standard UPID Format using flattened structure
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
-- Pattern template
(RANDOM_UUID(), 'UPID_CONFIG_PATTERN', 'STANDARD_UPID_FORMAT', 'FACILITY-NETWORK-SEQUENCE', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Network configuration: digits|format|value
(RANDOM_UUID(), 'UPID_CONFIG_NETWORK', 'STANDARD_UPID_FORMAT', '2|NUMERIC|00', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Facility configuration: digits|format
(RANDOM_UUID(), 'UPID_CONFIG_FACILITY', 'STANDARD_UPID_FORMAT', '3|NUMERIC', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Sequence configuration: digits|format
(RANDOM_UUID(), 'UPID_CONFIG_SEQUENCE', 'STANDARD_UPID_FORMAT', '8|SPLIT', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Separator
(RANDOM_UUID(), 'UPID_CONFIG_SEPARATOR', 'STANDARD_UPID_FORMAT', '-', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Hospital shortform: enabled|value
(RANDOM_UUID(), 'UPID_CONFIG_HOSPITAL', 'STANDARD_UPID_FORMAT', 'false|', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert Compact UPID configuration - Main Entry
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
VALUES (
    RANDOM_UUID(),
    'UPID_CONFIG',
    'COMPACT_UPID_FORMAT',
    'Compact UPID format without separators',
    99, -- Not default
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert components for Compact UPID Format using flattened structure
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
-- Pattern template
(RANDOM_UUID(), 'UPID_CONFIG_PATTERN', 'COMPACT_UPID_FORMAT', 'FACILITY-NETWORK-SEQUENCE', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Network configuration: digits|format|value
(RANDOM_UUID(), 'UPID_CONFIG_NETWORK', 'COMPACT_UPID_FORMAT', '2|NUMERIC|00', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Facility configuration: digits|format
(RANDOM_UUID(), 'UPID_CONFIG_FACILITY', 'COMPACT_UPID_FORMAT', '3|NUMERIC', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Sequence configuration: digits|format
(RANDOM_UUID(), 'UPID_CONFIG_SEQUENCE', 'COMPACT_UPID_FORMAT', '8|CONTINUOUS', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Separator (empty for compact)
(RANDOM_UUID(), 'UPID_CONFIG_SEPARATOR', 'COMPACT_UPID_FORMAT', '', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- Hospital shortform: enabled|value
(RANDOM_UUID(), 'UPID_CONFIG_HOSPITAL', 'COMPACT_UPID_FORMAT', 'false|', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert Hospital Shortform UPID configuration
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
VALUES (
    'a0000000-0000-0000-0000-000000000003',
    'UPID_CONFIG',
    'Hospital Shortform Format',
    'UPID format with hospital shortform prefix',
    99, -- Not default
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Insert components for Hospital Shortform UPID Format
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'PATTERN_TEMPLATE', 'HOSPITAL-FACILITY-SEQUENCE', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'NETWORK_ID_DIGITS', '2', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'NETWORK_ID_VALUE', '00', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'FACILITY_ID_DIGITS', '3', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'FACILITY_ID_FORMAT', 'NUMERIC', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'SEQUENCE_DIGITS', '8', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'SEQUENCE_FORMAT', 'SPLIT', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'SEPARATOR', '-', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'HOSPITAL_SHORTFORM', 'HSP', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_CONFIG_a0000000-0000-0000-0000-000000000003', 'USE_HOSPITAL_SHORTFORM', 'true', 0, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert predefined network ID options
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(RANDOM_UUID(), 'UPID_NETWORK_ID', '00', 'Default Network (00)', 1, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_NETWORK_ID', '01', 'Primary Network (01)', 2, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_NETWORK_ID', '02', 'Secondary Network (02)', 3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_NETWORK_ID', '99', 'Test Network (99)', 99, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert facility ID format options
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(RANDOM_UUID(), 'UPID_FACILITY_FORMAT', 'NUMERIC', 'Numeric (001, 123)', 1, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_FACILITY_FORMAT', 'ALPHA', 'Alphabetic (ABC, XYZ)', 2, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_FACILITY_FORMAT', 'ALPHANUMERIC', 'Alphanumeric (A01, X1Y)', 3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sequence format options
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(RANDOM_UUID(), 'UPID_SEQUENCE_FORMAT', 'SPLIT', 'Split format (0000-0001)', 1, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_SEQUENCE_FORMAT', 'CONTINUOUS', 'Continuous format (00000001)', 2, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert common hospital shortforms
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(RANDOM_UUID(), 'UPID_HOSPITAL_SHORTFORM', 'HSP', 'Hospital (HSP)', 1, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_HOSPITAL_SHORTFORM', 'MED', 'Medical Center (MED)', 2, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_HOSPITAL_SHORTFORM', 'CARE', 'Care Center (CARE)', 3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_HOSPITAL_SHORTFORM', 'CLINIC', 'Clinic (CLINIC)', 4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(RANDOM_UUID(), 'UPID_HOSPITAL_SHORTFORM', 'INST', 'Institute (INST)', 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
