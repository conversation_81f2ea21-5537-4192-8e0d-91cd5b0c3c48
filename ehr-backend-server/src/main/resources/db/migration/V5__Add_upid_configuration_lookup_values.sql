-- Migration V5: Add UPID Configuration Lookup Values
-- Description: Add UPID configuration data to the existing lookup_values table

-- Insert default UPID configuration (Standard Format)
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    'UPID_CONFIG',
    'Standard UPID Format',
    'Standard UPID format with facility prefix, network ID, and split sequence number',
    1, -- Default configuration (sort_order = 1)
    true,
    NOW(),
    NOW()
);

-- Get the ID of the standard configuration for component storage
-- We'll use a fixed UUID for consistency
DO $$
DECLARE
    standard_config_id UUID := 'a0000000-0000-0000-0000-000000000001';
BEGIN
    -- Update the standard config with our fixed UUID
    UPDATE lookup_values 
    SET id = standard_config_id 
    WHERE category = 'UPID_CONFIG' AND code = 'Standard UPID Format';

    -- Insert components for Standard UPID Format
    INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'PATTERN_TEMPLATE', 'FACILITY-NETWORK-SEQUENCE', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'NETWORK_ID_DIGITS', '2', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'NETWORK_ID_VALUE', '00', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'FACILITY_ID_DIGITS', '3', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'FACILITY_ID_FORMAT', 'NUMERIC', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'SEQUENCE_DIGITS', '8', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'SEQUENCE_FORMAT', 'SPLIT', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'SEPARATOR', '-', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || standard_config_id, 'USE_HOSPITAL_SHORTFORM', 'false', 0, true, NOW(), NOW());
END $$;

-- Insert Compact UPID configuration
DO $$
DECLARE
    compact_config_id UUID := 'a0000000-0000-0000-0000-000000000002';
BEGIN
    -- Insert main compact configuration
    INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
    VALUES (
        compact_config_id,
        'UPID_CONFIG',
        'Compact UPID Format',
        'Compact UPID format without separators',
        99, -- Not default
        true,
        NOW(),
        NOW()
    );

    -- Insert components for Compact UPID Format
    INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'PATTERN_TEMPLATE', 'FACILITY-NETWORK-SEQUENCE', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'NETWORK_ID_DIGITS', '2', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'NETWORK_ID_VALUE', '00', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'FACILITY_ID_DIGITS', '3', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'FACILITY_ID_FORMAT', 'NUMERIC', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'SEQUENCE_DIGITS', '8', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'SEQUENCE_FORMAT', 'CONTINUOUS', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'SEPARATOR', '', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || compact_config_id, 'USE_HOSPITAL_SHORTFORM', 'false', 0, true, NOW(), NOW());
END $$;

-- Insert Hospital Shortform UPID configuration
DO $$
DECLARE
    hospital_config_id UUID := 'a0000000-0000-0000-0000-000000000003';
BEGIN
    -- Insert main hospital configuration
    INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
    VALUES (
        hospital_config_id,
        'UPID_CONFIG',
        'Hospital Shortform Format',
        'UPID format with hospital shortform prefix',
        99, -- Not default
        true,
        NOW(),
        NOW()
    );

    -- Insert components for Hospital Shortform UPID Format
    INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'PATTERN_TEMPLATE', 'HOSPITAL-FACILITY-SEQUENCE', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'NETWORK_ID_DIGITS', '2', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'NETWORK_ID_VALUE', '00', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'FACILITY_ID_DIGITS', '3', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'FACILITY_ID_FORMAT', 'NUMERIC', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'SEQUENCE_DIGITS', '8', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'SEQUENCE_FORMAT', 'SPLIT', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'SEPARATOR', '-', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'HOSPITAL_SHORTFORM', 'HSP', 0, true, NOW(), NOW()),
    (gen_random_uuid(), 'UPID_CONFIG_' || hospital_config_id, 'USE_HOSPITAL_SHORTFORM', 'true', 0, true, NOW(), NOW());
END $$;

-- Insert predefined network ID options
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(gen_random_uuid(), 'UPID_NETWORK_ID', '00', 'Default Network (00)', 1, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_NETWORK_ID', '01', 'Primary Network (01)', 2, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_NETWORK_ID', '02', 'Secondary Network (02)', 3, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_NETWORK_ID', '99', 'Test Network (99)', 99, true, NOW(), NOW());

-- Insert facility ID format options
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(gen_random_uuid(), 'UPID_FACILITY_FORMAT', 'NUMERIC', 'Numeric (001, 123)', 1, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_FACILITY_FORMAT', 'ALPHA', 'Alphabetic (ABC, XYZ)', 2, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_FACILITY_FORMAT', 'ALPHANUMERIC', 'Alphanumeric (A01, X1Y)', 3, true, NOW(), NOW());

-- Insert sequence format options
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(gen_random_uuid(), 'UPID_SEQUENCE_FORMAT', 'SPLIT', 'Split format (0000-0001)', 1, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_SEQUENCE_FORMAT', 'CONTINUOUS', 'Continuous format (00000001)', 2, true, NOW(), NOW());

-- Insert common hospital shortforms
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at) VALUES
(gen_random_uuid(), 'UPID_HOSPITAL_SHORTFORM', 'HSP', 'Hospital (HSP)', 1, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_HOSPITAL_SHORTFORM', 'MED', 'Medical Center (MED)', 2, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_HOSPITAL_SHORTFORM', 'CARE', 'Care Center (CARE)', 3, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_HOSPITAL_SHORTFORM', 'CLINIC', 'Clinic (CLINIC)', 4, true, NOW(), NOW()),
(gen_random_uuid(), 'UPID_HOSPITAL_SHORTFORM', 'INST', 'Institute (INST)', 5, true, NOW(), NOW());

-- Create history entries for initial configurations
INSERT INTO lookup_values (id, category, code, display_name, sort_order, active, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    'UPID_CONFIG_HISTORY',
    id || '_' || EXTRACT(EPOCH FROM NOW())::bigint,
    'CREATE|SYSTEM|System initialization||Initial configuration: ' || code,
    0,
    true,
    NOW(),
    NOW()
FROM lookup_values 
WHERE category = 'UPID_CONFIG';

-- Add comments for documentation
COMMENT ON TABLE lookup_values IS 'Lookup values table now includes UPID configuration data with categories: UPID_CONFIG, UPID_NETWORK_ID, UPID_FACILITY_FORMAT, UPID_SEQUENCE_FORMAT, UPID_HOSPITAL_SHORTFORM, UPID_SEQUENCE_COUNTER, UPID_CONFIG_HISTORY';

-- Create indexes for better performance on UPID configuration queries
CREATE INDEX IF NOT EXISTS idx_lookup_values_upid_config ON lookup_values(category, code) WHERE category LIKE 'UPID_%';
CREATE INDEX IF NOT EXISTS idx_lookup_values_upid_active ON lookup_values(category, active, sort_order) WHERE category LIKE 'UPID_%';
CREATE INDEX IF NOT EXISTS idx_lookup_values_upid_history ON lookup_values(category, created_at) WHERE category = 'UPID_CONFIG_HISTORY';
