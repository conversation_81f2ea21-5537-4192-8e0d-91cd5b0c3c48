-- Migration V5: Create UPID Configuration Tables
-- Description: Create tables for advanced UPID configuration with regex patterns and configurable components

-- Create UPID configuration table
CREATE TABLE upid_configuration (
    config_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_name <PERSON><PERSON>HAR(100) NOT NULL UNIQUE,
    pattern_regex VARCHAR(500) NOT NULL,
    pattern_template VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Component configurations
    network_id_digits INT NOT NULL DEFAULT 2,
    network_id_value VARCHAR(10) NOT NULL DEFAULT '00',
    facility_id_digits INT NOT NULL DEFAULT 3,
    facility_id_format VARCHAR(50) NOT NULL DEFAULT 'NUMERIC',
    sequence_digits INT NOT NULL DEFAULT 8,
    sequence_format VARCHAR(50) NOT NULL DEFAULT 'SPLIT',
    separator VARCHAR(5) NOT NULL DEFAULT '-',
    hospital_shortform VARCHAR(20),
    use_hospital_shortform BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Pattern examples and validation
    example_pattern VARCHAR(100) NOT NULL DEFAULT '',
    validation_regex VARCHAR(500) NOT NULL DEFAULT '',
    
    -- Configuration metadata
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT NOT NULL DEFAULT 0,
    
    -- Constraints
    CONSTRAINT chk_network_id_digits CHECK (network_id_digits >= 1 AND network_id_digits <= 5),
    CONSTRAINT chk_facility_id_digits CHECK (facility_id_digits >= 1 AND facility_id_digits <= 10),
    CONSTRAINT chk_sequence_digits CHECK (sequence_digits >= 4 AND sequence_digits <= 12),
    CONSTRAINT chk_facility_id_format CHECK (facility_id_format IN ('NUMERIC', 'ALPHA', 'ALPHANUMERIC')),
    CONSTRAINT chk_sequence_format CHECK (sequence_format IN ('SPLIT', 'CONTINUOUS'))
);

-- Create index on active configurations
CREATE INDEX idx_upid_config_active ON upid_configuration(is_active);
CREATE INDEX idx_upid_config_default ON upid_configuration(is_default);
CREATE INDEX idx_upid_config_name ON upid_configuration(config_name);

-- Create UPID configuration history table
CREATE TABLE upid_configuration_history (
    history_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_id BIGINT NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values TEXT,
    new_values TEXT,
    changed_by VARCHAR(100) NOT NULL,
    change_reason VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_upid_history_config FOREIGN KEY (config_id) REFERENCES upid_configuration(config_id),
    
    -- Constraints
    CONSTRAINT chk_upid_history_action CHECK (action IN ('CREATE', 'UPDATE', 'DELETE', 'ACTIVATE', 'DEACTIVATE', 'SET_DEFAULT'))
);

-- Create indexes on history table
CREATE INDEX idx_upid_history_config_id ON upid_configuration_history(config_id);
CREATE INDEX idx_upid_history_action ON upid_configuration_history(action);
CREATE INDEX idx_upid_history_created_at ON upid_configuration_history(created_at);
CREATE INDEX idx_upid_history_changed_by ON upid_configuration_history(changed_by);

-- Create UPID sequence counter table
CREATE TABLE upid_sequence_counter (
    counter_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    facility_id VARCHAR(20) NOT NULL,
    config_id BIGINT NOT NULL,
    current_sequence BIGINT NOT NULL DEFAULT 0,
    last_generated_upid VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_upid_counter_config FOREIGN KEY (config_id) REFERENCES upid_configuration(config_id),
    
    -- Unique constraint on facility_id and config_id
    CONSTRAINT uk_upid_counter_facility_config UNIQUE (facility_id, config_id),
    
    -- Constraints
    CONSTRAINT chk_upid_counter_sequence CHECK (current_sequence >= 0)
);

-- Create indexes on sequence counter table
CREATE INDEX idx_upid_counter_facility_id ON upid_sequence_counter(facility_id);
CREATE INDEX idx_upid_counter_config_id ON upid_sequence_counter(config_id);
CREATE INDEX idx_upid_counter_updated_at ON upid_sequence_counter(updated_at);

-- Insert default UPID configuration
INSERT INTO upid_configuration (
    config_name,
    pattern_regex,
    pattern_template,
    description,
    network_id_digits,
    network_id_value,
    facility_id_digits,
    facility_id_format,
    sequence_digits,
    sequence_format,
    separator,
    example_pattern,
    validation_regex,
    is_active,
    is_default,
    created_by,
    updated_by
) VALUES (
    'Standard UPID Format',
    '^\\d{3}-\\d{2}-\\d{4}-\\d{4}$',
    'FACILITY-NETWORK-SEQUENCE',
    'Standard UPID format with facility prefix, network ID, and split sequence number',
    2,
    '00',
    3,
    'NUMERIC',
    8,
    'SPLIT',
    '-',
    '001-00-0000-0001',
    '^\\d{3}-\\d{2}-\\d{4}-\\d{4}$',
    TRUE,
    TRUE,
    'SYSTEM',
    'SYSTEM'
);

-- Insert alternative UPID configurations
INSERT INTO upid_configuration (
    config_name,
    pattern_regex,
    pattern_template,
    description,
    network_id_digits,
    network_id_value,
    facility_id_digits,
    facility_id_format,
    sequence_digits,
    sequence_format,
    separator,
    example_pattern,
    validation_regex,
    is_active,
    is_default,
    created_by,
    updated_by
) VALUES (
    'Compact UPID Format',
    '^\\d{3}\\d{2}\\d{8}$',
    'FACILITY-NETWORK-SEQUENCE',
    'Compact UPID format without separators',
    2,
    '00',
    3,
    'NUMERIC',
    8,
    'CONTINUOUS',
    '',
    '00100000001',
    '^\\d{3}\\d{2}\\d{8}$',
    TRUE,
    FALSE,
    'SYSTEM',
    'SYSTEM'
);

INSERT INTO upid_configuration (
    config_name,
    pattern_regex,
    pattern_template,
    description,
    network_id_digits,
    network_id_value,
    facility_id_digits,
    facility_id_format,
    sequence_digits,
    sequence_format,
    separator,
    hospital_shortform,
    use_hospital_shortform,
    example_pattern,
    validation_regex,
    is_active,
    is_default,
    created_by,
    updated_by
) VALUES (
    'Hospital Shortform Format',
    '^[A-Z]{3}-\\d{3}-\\d{4}-\\d{4}$',
    'HOSPITAL-FACILITY-SEQUENCE',
    'UPID format with hospital shortform prefix',
    2,
    '00',
    3,
    'NUMERIC',
    8,
    'SPLIT',
    '-',
    'HSP',
    TRUE,
    'HSP-001-0000-0001',
    '^[A-Z]{3}-\\d{3}-\\d{4}-\\d{4}$',
    TRUE,
    FALSE,
    'SYSTEM',
    'SYSTEM'
);

-- Create history entries for initial configurations
INSERT INTO upid_configuration_history (config_id, action, new_values, changed_by, change_reason)
SELECT config_id, 'CREATE', CONCAT('Initial configuration: ', config_name), 'SYSTEM', 'System initialization'
FROM upid_configuration;

-- Add comments to tables
ALTER TABLE upid_configuration COMMENT = 'UPID configuration table with regex patterns and configurable components';
ALTER TABLE upid_configuration_history COMMENT = 'Audit trail for UPID configuration changes';
ALTER TABLE upid_sequence_counter COMMENT = 'Sequence counters for UPID generation per facility and configuration';
