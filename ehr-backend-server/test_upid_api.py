#!/usr/bin/env python3
"""
Test script for UPID Configuration API
This script tests the UPID configuration endpoints
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:8080"
ADMIN_API_BASE = f"{BASE_URL}/api/admin/upid-config"

def test_api_endpoint(endpoint, method="GET", data=None, expected_status=200):
    """Test an API endpoint"""
    try:
        if method == "GET":
            response = requests.get(endpoint, timeout=10)
        elif method == "POST":
            response = requests.post(endpoint, json=data, headers={"Content-Type": "application/json"}, timeout=10)
        elif method == "PUT":
            response = requests.put(endpoint, json=data, headers={"Content-Type": "application/json"}, timeout=10)
        elif method == "DELETE":
            response = requests.delete(endpoint, timeout=10)
        
        print(f"✓ {method} {endpoint}")
        print(f"  Status: {response.status_code}")
        
        if response.status_code == expected_status:
            try:
                result = response.json()
                print(f"  Response: {json.dumps(result, indent=2)}")
                return True, result
            except:
                print(f"  Response: {response.text}")
                return True, response.text
        else:
            print(f"  ✗ Expected status {expected_status}, got {response.status_code}")
            print(f"  Response: {response.text}")
            return False, None
            
    except requests.exceptions.ConnectionError:
        print(f"✗ {method} {endpoint} - Connection failed (server not running?)")
        return False, None
    except Exception as e:
        print(f"✗ {method} {endpoint} - Error: {e}")
        return False, None

def test_upid_configuration_api():
    """Test UPID Configuration API endpoints"""
    print("=== Testing UPID Configuration API ===\n")
    
    # Test 1: Get all configurations
    print("1. Testing GET all configurations")
    success, configs = test_api_endpoint(ADMIN_API_BASE)
    if not success:
        return False
    print()
    
    # Test 2: Get default configuration
    print("2. Testing GET default configuration")
    success, default_config = test_api_endpoint(f"{ADMIN_API_BASE}/default")
    if not success:
        return False
    print()
    
    # Test 3: Validate pattern
    print("3. Testing pattern validation")
    validation_request = {
        "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
        "components": {
            "networkId": {
                "digits": 2,
                "format": "NUMERIC",
                "value": "00"
            },
            "facilityId": {
                "digits": 3,
                "format": "NUMERIC"
            },
            "sequence": {
                "digits": 8,
                "format": "SPLIT"
            },
            "separator": "-"
        },
        "testFacilityId": "1",
        "sampleCount": 3
    }
    success, validation_result = test_api_endpoint(f"{ADMIN_API_BASE}/validate-pattern", "POST", validation_request)
    if not success:
        return False
    print()
    
    # Test 4: Create new configuration
    print("4. Testing create new configuration")
    create_request = {
        "configName": "Test API Configuration",
        "patternTemplate": "HOSPITAL-FACILITY-SEQUENCE",
        "description": "Test configuration created via API",
        "components": {
            "networkId": {
                "digits": 2,
                "format": "NUMERIC",
                "value": "01"
            },
            "facilityId": {
                "digits": 3,
                "format": "NUMERIC"
            },
            "sequence": {
                "digits": 8,
                "format": "SPLIT"
            },
            "separator": "-",
            "hospitalShortform": {
                "enabled": True,
                "value": "TEST"
            }
        },
        "isDefault": False
    }
    success, new_config = test_api_endpoint(ADMIN_API_BASE, "POST", create_request, 201)
    if not success:
        return False
    
    config_id = new_config.get("configId") if new_config else None
    print()
    
    # Test 5: Generate UPID using new configuration
    if config_id:
        print("5. Testing UPID generation")
        success, upid_result = test_api_endpoint(f"{ADMIN_API_BASE}/{config_id}/generate/5", "POST")
        if not success:
            return False
        print()
        
        # Test 6: Get configuration by ID
        print("6. Testing GET configuration by ID")
        success, config_detail = test_api_endpoint(f"{ADMIN_API_BASE}/{config_id}")
        if not success:
            return False
        print()
        
        # Test 7: Get configuration history
        print("7. Testing GET configuration history")
        success, history = test_api_endpoint(f"{ADMIN_API_BASE}/{config_id}/history")
        if not success:
            return False
        print()
    
    print("=== All tests completed successfully! ===")
    return True

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/q/health", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("UPID Configuration API Test Script")
    print("==================================\n")
    
    # Check if server is running
    if not check_server_status():
        print("❌ Server is not running or not accessible at http://localhost:8080")
        print("Please start the server with: ./mvnw quarkus:dev")
        sys.exit(1)
    
    print("✅ Server is running\n")
    
    # Run tests
    if test_upid_configuration_api():
        print("\n🎉 All API tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
