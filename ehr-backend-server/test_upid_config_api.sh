#!/bin/bash

# Test script for Advanced UPID Configuration API
# This script demonstrates the key functionality of the UPID configuration system

BASE_URL="http://localhost:8080/api/admin/upid-config"

echo "=== Advanced UPID Configuration API Test ==="
echo "Base URL: $BASE_URL"
echo ""

# Function to make HTTP requests with proper formatting
make_request() {
    local method=$1
    local url=$2
    local data=$3
    
    echo ">>> $method $url"
    if [ -n "$data" ]; then
        echo "Request Body: $data"
    fi
    
    if [ -n "$data" ]; then
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -X $method "$url")
    fi
    
    echo "Response: $response"
    echo ""
    echo "$response"
}

# Test 1: Get all configurations (should show default configurations)
echo "=== Test 1: Get All Configurations ==="
make_request "GET" "$BASE_URL"

# Test 2: Get default configuration
echo "=== Test 2: Get Default Configuration ==="
make_request "GET" "$BASE_URL/default"

# Test 3: Validate a pattern
echo "=== Test 3: Validate Pattern ==="
validate_pattern_data='{
  "patternTemplate": "FACILITY-NETWORK-SEQUENCE",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "00"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-"
  },
  "testFacilityId": "1",
  "sampleCount": 3
}'
make_request "POST" "$BASE_URL/validate-pattern" "$validate_pattern_data"

# Test 4: Create a new configuration
echo "=== Test 4: Create New Configuration ==="
create_config_data='{
  "configName": "Test Hospital Format",
  "patternTemplate": "HOSPITAL-FACILITY-NETWORK-SEQUENCE",
  "description": "Test configuration with hospital shortform",
  "components": {
    "networkId": {
      "digits": 2,
      "format": "NUMERIC",
      "value": "01"
    },
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 8,
      "format": "SPLIT"
    },
    "separator": "-",
    "hospitalShortform": {
      "enabled": true,
      "value": "TEST",
      "position": "PREFIX"
    }
  },
  "isDefault": false
}'
new_config_response=$(make_request "POST" "$BASE_URL" "$create_config_data")

# Extract config ID from response (assuming it's in the response)
config_id=$(echo "$new_config_response" | grep -o '"configId":[0-9]*' | grep -o '[0-9]*' | head -1)

if [ -n "$config_id" ]; then
    echo "Created configuration with ID: $config_id"
    
    # Test 5: Get the created configuration
    echo "=== Test 5: Get Created Configuration ==="
    make_request "GET" "$BASE_URL/$config_id"
    
    # Test 6: Generate UPID using the new configuration
    echo "=== Test 6: Generate UPID ==="
    make_request "POST" "$BASE_URL/$config_id/generate/1"
    
    # Test 7: Update the configuration
    echo "=== Test 7: Update Configuration ==="
    update_config_data='{
      "description": "Updated test configuration",
      "components": {
        "sequence": {
          "digits": 10,
          "format": "CONTINUOUS"
        }
      },
      "changeReason": "Testing update functionality"
    }'
    make_request "PUT" "$BASE_URL/$config_id" "$update_config_data"
    
    # Test 8: Get configuration history
    echo "=== Test 8: Get Configuration History ==="
    make_request "GET" "$BASE_URL/$config_id/history"
    
    # Test 9: Generate another UPID to see the updated format
    echo "=== Test 9: Generate UPID with Updated Configuration ==="
    make_request "POST" "$BASE_URL/$config_id/generate/2"
    
    # Test 10: Delete the test configuration
    echo "=== Test 10: Delete Test Configuration ==="
    make_request "DELETE" "$BASE_URL/$config_id"
    
else
    echo "Failed to create configuration or extract config ID"
fi

# Test 11: Create a compact format configuration
echo "=== Test 11: Create Compact Format ==="
compact_config_data='{
  "configName": "Compact Test Format",
  "patternTemplate": "FACILITY-SEQUENCE",
  "description": "Compact format without separators",
  "components": {
    "facilityId": {
      "digits": 3,
      "format": "NUMERIC"
    },
    "sequence": {
      "digits": 6,
      "format": "CONTINUOUS"
    },
    "separator": ""
  },
  "isDefault": false
}'
make_request "POST" "$BASE_URL" "$compact_config_data"

# Test 12: Validate an invalid pattern
echo "=== Test 12: Validate Invalid Pattern ==="
invalid_pattern_data='{
  "patternTemplate": "INVALID-PATTERN",
  "components": {
    "facilityId": {
      "digits": 0,
      "format": "INVALID"
    },
    "sequence": {
      "digits": 1,
      "format": "UNKNOWN"
    },
    "separator": "-"
  },
  "testFacilityId": "1",
  "sampleCount": 1
}'
make_request "POST" "$BASE_URL/validate-pattern" "$invalid_pattern_data"

echo "=== Test Complete ==="
echo ""
echo "Summary of tests performed:"
echo "1. Get all configurations"
echo "2. Get default configuration"
echo "3. Validate pattern"
echo "4. Create new configuration"
echo "5. Get created configuration"
echo "6. Generate UPID"
echo "7. Update configuration"
echo "8. Get configuration history"
echo "9. Generate UPID with updated configuration"
echo "10. Delete test configuration"
echo "11. Create compact format"
echo "12. Validate invalid pattern"
echo ""
echo "Check the responses above to verify the API functionality."
